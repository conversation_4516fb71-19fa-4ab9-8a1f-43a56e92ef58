<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON></title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #ffffff;
            overflow: hidden;
        }
        
        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #f5f5f5;
            border-bottom: 1px solid #e0e0e0;
            padding: 8px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 40px;
            box-sizing: border-box;
        }
        
        .title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .controls {
            display: flex;
            gap: 8px;
        }
        
        .btn {
            background: #007acc;
            color: white;
            border: none;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #005a9e;
        }
        
        .webview-container {
            flex: 1;
            position: relative;
        }
        
        webview {
            width: 100%;
            height: 100%;
            border: none;
            display: block;
            background: white;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #666;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007acc;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">Qwen Chat</div>
            <div class="controls">
                <button class="btn" onclick="reloadWebview()">Reload</button>
                <button class="btn" onclick="clearSession()">Clear Session</button>
            </div>
        </div>
        <div class="webview-container">
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <div>Loading Qwen Chat...</div>
            </div>
            <iframe
                id="qwen-iframe"
                src="https://chat.qwen.ai/"
                frameborder="0"
                style="width: 100%; height: 100%; border: none;">
            </iframe>
        </div>
    </div>

    <script>
        const iframe = document.getElementById('qwen-iframe');
        const loading = document.getElementById('loading');

        // Hide loading after iframe loads
        iframe.addEventListener('load', () => {
            console.log('Qwen Chat iframe loaded');
            loading.style.display = 'none';
        });

        iframe.addEventListener('error', (event) => {
            console.error('Qwen Chat iframe failed to load:', event);
            loading.innerHTML = '<div>Failed to load Qwen Chat. <button onclick="reloadIframe()">Retry</button></div>';
        });

        // Hide loading after a timeout regardless
        setTimeout(() => {
            loading.style.display = 'none';
        }, 3000);

        function reloadIframe() {
            loading.style.display = 'block';
            iframe.src = iframe.src;
        }

        function clearSession() {
            if (confirm('Are you sure you want to clear the session? This will log you out.')) {
                // Reload iframe to clear session
                reloadIframe();
            }
        }
    </script>
</body>
</html>
