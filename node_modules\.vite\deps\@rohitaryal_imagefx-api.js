import {
  __privateAdd,
  __privateMethod,
  __publicField
} from "./chunk-Q4XP6UTR.js";

// node_modules/@rohitaryal/imagefx-api/dist/utils/request.js
var request = async function(req) {
  req.headers.set("Origin", "https://labs.google");
  req.headers.set("Referer", "https://labs.google");
  try {
    const reqs = await fetch(req.url, {
      body: req.body,
      method: req.method,
      headers: req.headers
    });
    const res = await reqs.text();
    if (!reqs.ok) {
      return {
        Err: new Error(res)
      };
    }
    return {
      Ok: res
    };
  } catch (err) {
    return {
      Err: err instanceof Error ? err : new Error("Failed to fetch.")
    };
  }
};

// node_modules/@rohitaryal/imagefx-api/dist/index.js
var _ImageFx_instances, checkToken_fn;
var ImageFx = class {
  constructor(credentials) {
    __privateAdd(this, _ImageFx_instances);
    __publicField(this, "credentials");
    if (!credentials.authorizationKey && !credentials.cookie) {
      throw new Error("Authorization token and Cookie both are missing.");
    }
    if (credentials.cookie && credentials.cookie.length < 70) {
      throw new Error("Invalid cookie was provided.");
    }
    this.credentials = structuredClone(credentials);
    if (this.credentials.cookie && !this.credentials.cookie.startsWith("__Secure-next-auth.session-token=")) {
      this.credentials.cookie = "__Secure-next-auth.session-token=" + this.credentials.cookie;
    }
  }
  async getAuthToken(mutate = false) {
    if (!this.credentials.cookie) {
      return { Err: new Error("Cookie is required for generating auth token.") };
    }
    const req = {
      url: "https://labs.google/fx/api/auth/session",
      method: "GET",
      headers: new Headers({ "Cookie": this.credentials.cookie })
    };
    const resp = await request(req);
    if (resp.Err || !resp.Ok) {
      return { Err: resp.Err };
    }
    try {
      const parsedResp = JSON.parse(resp.Ok);
      if (!parsedResp.access_token) {
        return { Err: Error("Access token is missing from response: " + resp.Ok) };
      }
      if (mutate) {
        this.credentials.authorizationKey = parsedResp.access_token;
      }
      return { Ok: parsedResp.access_token };
    } catch (err) {
      return { Err: Error("Failed to parse response: " + resp.Ok) };
    }
  }
  /**
  * Generate image from provided prompt
  */
  async generateImage(prompt) {
    const tokenRes = await __privateMethod(this, _ImageFx_instances, checkToken_fn).call(this);
    if (tokenRes.Err) {
      return { Err: tokenRes.Err };
    }
    if (prompt.model == "IMAGEN_4") {
      prompt.model = "IMAGEN_3_5";
    }
    const req = {
      method: "POST",
      body: JSON.stringify({
        userInput: {
          candidatesCount: prompt.count || 4,
          prompts: [prompt.prompt],
          seed: prompt.seed || 0
        },
        aspectRatio: prompt.aspectRatio || "IMAGE_ASPECT_RATIO_LANDSCAPE",
        modelInput: { modelNameType: prompt.model || "IMAGEN_3_5" },
        clientContext: { sessionId: ";1740658431200", tool: "IMAGE_FX" }
      }),
      url: "https://aisandbox-pa.googleapis.com/v1:runImageFx",
      headers: new Headers({ "Authorization": "Bearer " + this.credentials.authorizationKey })
    };
    const res = await request(req);
    if (res.Err || !res.Ok) {
      return { Err: res.Err };
    }
    try {
      const parsedRes = JSON.parse(res.Ok);
      const images = parsedRes.imagePanels[0].generatedImages;
      if (!Array.isArray(images)) {
        return {
          Err: Error("Invalid response recieved: " + res.Ok)
        };
      }
      return { Ok: images };
    } catch (err) {
      return { Err: Error("Failed to parse JSON: " + res.Ok) };
    }
  }
};
_ImageFx_instances = new WeakSet();
checkToken_fn = async function() {
  if (!this.credentials.authorizationKey && !this.credentials.cookie) {
    return { Err: Error("Authorization token and Cookie both are missing.") };
  }
  if (this.credentials.cookie && !this.credentials.authorizationKey) {
    await this.getAuthToken(true);
  }
  return { Ok: true };
};
var dist_default = ImageFx;
export {
  dist_default as default
};
//# sourceMappingURL=@rohitaryal_imagefx-api.js.map
