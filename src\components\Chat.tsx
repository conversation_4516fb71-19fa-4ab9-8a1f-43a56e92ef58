import React, { useState, useRef, useEffect } from 'react'
import { Send, Bot, User, MessageSquare, ChevronDown, Loader2, <PERSON><PERSON>, Check, Plus, Trash2, Edit3 } from 'lucide-react'
import { ChatMessage, ChatSession, AIModel } from '../types'

interface ChatProps {
  className?: string
}

// Declare puter as a global variable
declare global {
  interface Window {
    puter: {
      ai: {
        chat: (message: string, options?: { model?: string; stream?: boolean }) => Promise<any>
      }
    }
  }
}

const AI_MODELS: { value: AIModel; label: string; description: string }[] = [
  { 
    value: 'deepseek-reasoner', 
    label: 'DeepSeek Reasoner', 
    description: 'Advanced reasoning and problem-solving' 
  },
  { 
    value: 'gpt-4.1', 
    label: 'GPT-4.1', 
    description: 'Latest OpenAI model with enhanced capabilities' 
  },
  { 
    value: 'gpt-4o', 
    label: 'GPT-4o', 
    description: 'Optimized GPT-4 for faster responses' 
  },
  { 
    value: 'google/gemini-2.5-pro', 
    label: 'Gemini 2.5 Pro', 
    description: 'Google\'s most advanced language model' 
  },
  { 
    value: 'google/gemini-2.5-pro-preview-05-06', 
    label: 'Gemini 2.5 Pro Preview', 
    description: 'Preview version with latest features' 
  },
]

const Chat: React.FC<ChatProps> = ({ className = '' }) => {
  const [sessions, setSessions] = useState<ChatSession[]>([])
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null)
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [selectedModel, setSelectedModel] = useState<AIModel>('gpt-4o')
  const [isModelDropdownOpen, setIsModelDropdownOpen] = useState(false)
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null)
  const [isSidebarOpen, setIsSidebarOpen] = useState(true)
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null)
  const [editingTitle, setEditingTitle] = useState('')

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsModelDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const generateId = () => Math.random().toString(36).substr(2, 9)

  // Get current session
  const currentSession = sessions.find(s => s.id === currentSessionId)
  const messages = currentSession?.messages || []

  // Load sessions on component mount
  useEffect(() => {
    loadSessions()
  }, [])

  const loadSessions = async () => {
    try {
      const result = await window.electronAPI.loadChatSessions()
      if (result.success && result.sessions) {
        setSessions(result.sessions)
        // Set current session to the first one or create a new one
        if (result.sessions.length > 0) {
          setCurrentSessionId(result.sessions[0].id)
        } else {
          createNewSession()
        }
      } else {
        createNewSession()
      }
    } catch (error) {
      console.error('Failed to load chat sessions:', error)
      createNewSession()
    }
  }

  const saveSessions = async (updatedSessions: ChatSession[]) => {
    try {
      await window.electronAPI.saveChatSessions(updatedSessions)
    } catch (error) {
      console.error('Failed to save chat sessions:', error)
    }
  }

  const createNewSession = () => {
    const newSession: ChatSession = {
      id: generateId(),
      title: 'New Chat',
      messages: [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
      model: selectedModel
    }

    const updatedSessions = [newSession, ...sessions]
    setSessions(updatedSessions)
    setCurrentSessionId(newSession.id)
    saveSessions(updatedSessions)
  }

  const deleteSession = (sessionId: string) => {
    const updatedSessions = sessions.filter(s => s.id !== sessionId)
    setSessions(updatedSessions)

    if (currentSessionId === sessionId) {
      if (updatedSessions.length > 0) {
        setCurrentSessionId(updatedSessions[0].id)
      } else {
        createNewSession()
        return
      }
    }

    saveSessions(updatedSessions)
  }

  const updateSessionTitle = (sessionId: string, newTitle: string) => {
    const updatedSessions = sessions.map(s =>
      s.id === sessionId
        ? { ...s, title: newTitle, updatedAt: Date.now() }
        : s
    )
    setSessions(updatedSessions)
    saveSessions(updatedSessions)
    setEditingSessionId(null)
    setEditingTitle('')
  }

  const updateSessionMessages = (sessionId: string, newMessages: ChatMessage[]) => {
    const updatedSessions = sessions.map(s =>
      s.id === sessionId
        ? {
            ...s,
            messages: newMessages,
            updatedAt: Date.now(),
            title: s.title === 'New Chat' && newMessages.length > 0
              ? newMessages[0].content.slice(0, 50) + (newMessages[0].content.length > 50 ? '...' : '')
              : s.title
          }
        : s
    )
    setSessions(updatedSessions)
    saveSessions(updatedSessions)
  }

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading || !currentSessionId) return

    const userMessage: ChatMessage = {
      id: generateId(),
      content: inputMessage.trim(),
      role: 'user',
      timestamp: Date.now(),
      model: selectedModel
    }

    const currentMessages = [...messages, userMessage]
    updateSessionMessages(currentSessionId, currentMessages)
    setInputMessage('')
    setIsLoading(true)

    try {
      // Create assistant message placeholder
      const assistantMessageId = generateId()
      const assistantMessage: ChatMessage = {
        id: assistantMessageId,
        content: '',
        role: 'assistant',
        timestamp: Date.now(),
        model: selectedModel,
        isStreaming: true
      }

      const messagesWithAssistant = [...currentMessages, assistantMessage]
      updateSessionMessages(currentSessionId, messagesWithAssistant)

      // Use Puter.js to get AI response with streaming
      const response = await window.puter.ai.chat(userMessage.content, {
        model: selectedModel,
        stream: true
      })

      let fullContent = ''

      // Handle streaming response
      for await (const part of response) {
        if (part?.text) {
          fullContent += part.text
          const updatedMessages = messagesWithAssistant.map(msg =>
            msg.id === assistantMessageId
              ? { ...msg, content: fullContent }
              : msg
          )
          updateSessionMessages(currentSessionId, updatedMessages)
        }
      }

      // Mark streaming as complete
      const finalMessages = messagesWithAssistant.map(msg =>
        msg.id === assistantMessageId
          ? { ...msg, isStreaming: false }
          : msg
      )
      updateSessionMessages(currentSessionId, finalMessages)

    } catch (error) {
      console.error('Error sending message:', error)

      // Add error message
      const errorMessage: ChatMessage = {
        id: generateId(),
        content: 'Sorry, I encountered an error while processing your message. Please try again.',
        role: 'assistant',
        timestamp: Date.now(),
        model: selectedModel
      }

      const errorMessages = [...currentMessages, errorMessage]
      updateSessionMessages(currentSessionId, errorMessages)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const copyToClipboard = async (content: string, messageId: string) => {
    try {
      await navigator.clipboard.writeText(content)
      setCopiedMessageId(messageId)
      setTimeout(() => setCopiedMessageId(null), 2000)
    } catch (error) {
      console.error('Failed to copy text:', error)
    }
  }

  const formatMessage = (content: string) => {
    // Simple markdown-like formatting
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code class="bg-gray-800 px-1 py-0.5 rounded text-sm">$1</code>')
      .replace(/\n/g, '<br>')
  }

  return (
    <div className={`flex h-full bg-app ${className}`}>
      {/* Sessions Sidebar */}
      {isSidebarOpen && (
        <div className="w-80 bg-sidebar border-r border-matte flex flex-col">
          {/* Sidebar Header */}
          <div className="p-4 border-b border-matte">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-xl bg-gradient-to-br from-accent-primary to-accent-muted shadow-glow-sm">
                  <MessageSquare className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold text-white tracking-tight">
                    AI Chat
                  </h1>
                  <p className="text-xs text-gray-400 font-medium">Chat sessions</p>
                </div>
              </div>
            </div>

            <button
              onClick={createNewSession}
              className="w-full flex items-center gap-2 px-4 py-2 bg-accent-primary text-white rounded-xl hover:bg-accent-primary/80 transition-colors"
            >
              <Plus className="w-4 h-4" />
              New Chat
            </button>
          </div>

          {/* Sessions List */}
          <div className="flex-1 overflow-y-auto custom-scrollbar">
            {sessions.map((session) => (
              <div
                key={session.id}
                className={`group relative p-3 border-b border-matte/30 cursor-pointer hover:bg-matte/30 transition-colors ${
                  currentSessionId === session.id ? 'bg-accent-primary/10 border-l-2 border-l-accent-primary' : ''
                }`}
                onClick={() => setCurrentSessionId(session.id)}
              >
                {editingSessionId === session.id ? (
                  <input
                    type="text"
                    value={editingTitle}
                    onChange={(e) => setEditingTitle(e.target.value)}
                    onBlur={() => updateSessionTitle(session.id, editingTitle)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        updateSessionTitle(session.id, editingTitle)
                      }
                    }}
                    className="w-full bg-input border border-matte rounded px-2 py-1 text-sm text-white focus:outline-none focus:border-accent-primary/50"
                    autoFocus
                  />
                ) : (
                  <>
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium text-white truncate pr-2">
                        {session.title}
                      </h3>
                      <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            setEditingSessionId(session.id)
                            setEditingTitle(session.title)
                          }}
                          className="p-1 hover:bg-matte/50 rounded transition-colors"
                          title="Rename"
                        >
                          <Edit3 className="w-3 h-3 text-gray-400" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteSession(session.id)
                          }}
                          className="p-1 hover:bg-red-500/20 rounded transition-colors"
                          title="Delete"
                        >
                          <Trash2 className="w-3 h-3 text-red-400" />
                        </button>
                      </div>
                    </div>
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-xs text-gray-400">
                        {session.messages.length} messages
                      </span>
                      <span className="text-xs text-gray-400">
                        {new Date(session.updatedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-matte">
          <div className="flex items-center gap-3">
            {!isSidebarOpen && (
              <button
                onClick={() => setIsSidebarOpen(true)}
                className="p-2 hover:bg-matte/50 rounded-lg transition-colors"
              >
                <MessageSquare className="w-5 h-5 text-gray-400" />
              </button>
            )}
            <div>
              <h2 className="text-lg font-semibold text-white">
                {currentSession?.title || 'Select a chat'}
              </h2>
              <p className="text-xs text-gray-400">
                {currentSession ? `${messages.length} messages` : 'No active session'}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {isSidebarOpen && (
              <button
                onClick={() => setIsSidebarOpen(false)}
                className="p-2 hover:bg-matte/50 rounded-lg transition-colors"
                title="Hide sidebar"
              >
                <ChevronDown className="w-4 h-4 text-gray-400 rotate-90" />
              </button>
            )}

            {/* Model Selector */}
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => setIsModelDropdownOpen(!isModelDropdownOpen)}
                className="flex items-center gap-2 px-3 py-2 bg-input border border-matte rounded-lg text-white hover:border-accent-primary/50 transition-colors"
              >
                <Bot className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {AI_MODELS.find(m => m.value === selectedModel)?.label}
                </span>
                <ChevronDown className="w-4 h-4" />
              </button>

              {isModelDropdownOpen && (
                <div className="absolute top-full right-0 mt-2 w-80 bg-card border border-matte rounded-xl shadow-xl z-50 overflow-hidden">
                  {AI_MODELS.map((model) => (
                    <button
                      key={model.value}
                      onClick={() => {
                        setSelectedModel(model.value)
                        setIsModelDropdownOpen(false)
                      }}
                      className={`w-full text-left px-4 py-3 hover:bg-matte/50 transition-colors border-b border-matte/30 last:border-b-0 ${
                        selectedModel === model.value ? 'bg-accent-primary/10 text-accent-primary' : 'text-white'
                      }`}
                    >
                      <div className="font-medium text-sm">{model.label}</div>
                      <div className="text-xs text-gray-400 mt-1">{model.description}</div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4 custom-scrollbar">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="p-4 rounded-full bg-gradient-to-br from-accent-primary/20 to-accent-muted/20 border border-accent-primary/30 mb-4">
              <MessageSquare className="w-8 h-8 text-accent-primary" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Start a conversation</h3>
            <p className="text-gray-400 text-sm max-w-md">
              Choose an AI model and start chatting. All models are free and unlimited through Puter.js.
            </p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              {message.role === 'assistant' && (
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-accent-primary to-accent-muted flex items-center justify-center">
                  <Bot className="w-4 h-4 text-white" />
                </div>
              )}
              
              <div className={`max-w-[70%] ${message.role === 'user' ? 'order-1' : ''}`}>
                <div
                  className={`rounded-2xl px-4 py-3 ${
                    message.role === 'user'
                      ? 'bg-accent-primary text-white'
                      : 'bg-card border border-matte text-white'
                  }`}
                >
                  <div 
                    className="text-sm leading-relaxed"
                    dangerouslySetInnerHTML={{ __html: formatMessage(message.content) }}
                  />
                  {message.isStreaming && (
                    <div className="flex items-center gap-1 mt-2">
                      <Loader2 className="w-3 h-3 animate-spin" />
                      <span className="text-xs text-gray-400">Thinking...</span>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center gap-2 mt-2 text-xs text-gray-400">
                  <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
                  {message.model && (
                    <>
                      <span>•</span>
                      <span>{AI_MODELS.find(m => m.value === message.model)?.label}</span>
                    </>
                  )}
                  {message.role === 'assistant' && (
                    <button
                      onClick={() => copyToClipboard(message.content, message.id)}
                      className="ml-2 p-1 hover:bg-matte/50 rounded transition-colors"
                      title="Copy message"
                    >
                      {copiedMessageId === message.id ? (
                        <Check className="w-3 h-3 text-green-400" />
                      ) : (
                        <Copy className="w-3 h-3" />
                      )}
                    </button>
                  )}
                </div>
              </div>

              {message.role === 'user' && (
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
              )}
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-6 border-t border-matte">
        <div className="flex gap-3">
          <div className="flex-1 relative">
            <textarea
              ref={inputRef}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message..."
              className="w-full bg-input border border-matte rounded-xl px-4 py-3 text-white placeholder-gray-400 resize-none focus:outline-none focus:border-accent-primary/50 transition-colors"
              rows={1}
              style={{ minHeight: '48px', maxHeight: '120px' }}
              disabled={isLoading}
            />
          </div>
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isLoading}
            className="px-4 py-3 bg-accent-primary text-white rounded-xl hover:bg-accent-primary/80 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
          >
            {isLoading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
          </button>
        </div>
      </div>
      </div>
    </div>
  )
}

export default Chat
