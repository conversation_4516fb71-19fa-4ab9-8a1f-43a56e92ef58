var Td=Object.defineProperty;var Go=e=>{throw TypeError(e)};var Ld=(e,t,n)=>t in e?Td(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Gr=(e,t,n)=>Ld(e,typeof t!="symbol"?t+"":t,n),Dd=(e,t,n)=>t.has(e)||Go("Cannot "+n);var Yo=(e,t,n)=>t.has(e)?Go("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n);var Ko=(e,t,n)=>(Dd(e,t,"access private method"),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const a of l)if(a.type==="childList")for(const o of a.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(l){const a={};return l.integrity&&(a.integrity=l.integrity),l.referrerPolicy&&(a.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?a.credentials="include":l.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function r(l){if(l.ep)return;l.ep=!0;const a=n(l);fetch(l.href,a)}})();function Rd(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var gu={exports:{}},ql={},xu={exports:{}},Z={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Or=Symbol.for("react.element"),Fd=Symbol.for("react.portal"),Od=Symbol.for("react.fragment"),$d=Symbol.for("react.strict_mode"),Ud=Symbol.for("react.profiler"),Hd=Symbol.for("react.provider"),Bd=Symbol.for("react.context"),Vd=Symbol.for("react.forward_ref"),Wd=Symbol.for("react.suspense"),Xd=Symbol.for("react.memo"),Qd=Symbol.for("react.lazy"),Zo=Symbol.iterator;function Gd(e){return e===null||typeof e!="object"?null:(e=Zo&&e[Zo]||e["@@iterator"],typeof e=="function"?e:null)}var yu={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},vu=Object.assign,wu={};function Wn(e,t,n){this.props=e,this.context=t,this.refs=wu,this.updater=n||yu}Wn.prototype.isReactComponent={};Wn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Wn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function ju(){}ju.prototype=Wn.prototype;function Qa(e,t,n){this.props=e,this.context=t,this.refs=wu,this.updater=n||yu}var Ga=Qa.prototype=new ju;Ga.constructor=Qa;vu(Ga,Wn.prototype);Ga.isPureReactComponent=!0;var qo=Array.isArray,Nu=Object.prototype.hasOwnProperty,Ya={current:null},ku={key:!0,ref:!0,__self:!0,__source:!0};function Cu(e,t,n){var r,l={},a=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(a=""+t.key),t)Nu.call(t,r)&&!ku.hasOwnProperty(r)&&(l[r]=t[r]);var i=arguments.length-2;if(i===1)l.children=n;else if(1<i){for(var u=Array(i),c=0;c<i;c++)u[c]=arguments[c+2];l.children=u}if(e&&e.defaultProps)for(r in i=e.defaultProps,i)l[r]===void 0&&(l[r]=i[r]);return{$$typeof:Or,type:e,key:a,ref:o,props:l,_owner:Ya.current}}function Yd(e,t){return{$$typeof:Or,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ka(e){return typeof e=="object"&&e!==null&&e.$$typeof===Or}function Kd(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Jo=/\/+/g;function bs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Kd(""+e.key):t.toString(36)}function ml(e,t,n,r,l){var a=typeof e;(a==="undefined"||a==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(a){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case Or:case Fd:o=!0}}if(o)return o=e,l=l(o),e=r===""?"."+bs(o,0):r,qo(l)?(n="",e!=null&&(n=e.replace(Jo,"$&/")+"/"),ml(l,t,n,"",function(c){return c})):l!=null&&(Ka(l)&&(l=Yd(l,n+(!l.key||o&&o.key===l.key?"":(""+l.key).replace(Jo,"$&/")+"/")+e)),t.push(l)),1;if(o=0,r=r===""?".":r+":",qo(e))for(var i=0;i<e.length;i++){a=e[i];var u=r+bs(a,i);o+=ml(a,t,n,u,l)}else if(u=Gd(e),typeof u=="function")for(e=u.call(e),i=0;!(a=e.next()).done;)a=a.value,u=r+bs(a,i++),o+=ml(a,t,n,u,l);else if(a==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function Yr(e,t,n){if(e==null)return e;var r=[],l=0;return ml(e,r,"","",function(a){return t.call(n,a,l++)}),r}function Zd(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var $e={current:null},pl={transition:null},qd={ReactCurrentDispatcher:$e,ReactCurrentBatchConfig:pl,ReactCurrentOwner:Ya};function Su(){throw Error("act(...) is not supported in production builds of React.")}Z.Children={map:Yr,forEach:function(e,t,n){Yr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Yr(e,function(){t++}),t},toArray:function(e){return Yr(e,function(t){return t})||[]},only:function(e){if(!Ka(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Z.Component=Wn;Z.Fragment=Od;Z.Profiler=Ud;Z.PureComponent=Qa;Z.StrictMode=$d;Z.Suspense=Wd;Z.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=qd;Z.act=Su;Z.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=vu({},e.props),l=e.key,a=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(a=t.ref,o=Ya.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var i=e.type.defaultProps;for(u in t)Nu.call(t,u)&&!ku.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&i!==void 0?i[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){i=Array(u);for(var c=0;c<u;c++)i[c]=arguments[c+2];r.children=i}return{$$typeof:Or,type:e.type,key:l,ref:a,props:r,_owner:o}};Z.createContext=function(e){return e={$$typeof:Bd,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Hd,_context:e},e.Consumer=e};Z.createElement=Cu;Z.createFactory=function(e){var t=Cu.bind(null,e);return t.type=e,t};Z.createRef=function(){return{current:null}};Z.forwardRef=function(e){return{$$typeof:Vd,render:e}};Z.isValidElement=Ka;Z.lazy=function(e){return{$$typeof:Qd,_payload:{_status:-1,_result:e},_init:Zd}};Z.memo=function(e,t){return{$$typeof:Xd,type:e,compare:t===void 0?null:t}};Z.startTransition=function(e){var t=pl.transition;pl.transition={};try{e()}finally{pl.transition=t}};Z.unstable_act=Su;Z.useCallback=function(e,t){return $e.current.useCallback(e,t)};Z.useContext=function(e){return $e.current.useContext(e)};Z.useDebugValue=function(){};Z.useDeferredValue=function(e){return $e.current.useDeferredValue(e)};Z.useEffect=function(e,t){return $e.current.useEffect(e,t)};Z.useId=function(){return $e.current.useId()};Z.useImperativeHandle=function(e,t,n){return $e.current.useImperativeHandle(e,t,n)};Z.useInsertionEffect=function(e,t){return $e.current.useInsertionEffect(e,t)};Z.useLayoutEffect=function(e,t){return $e.current.useLayoutEffect(e,t)};Z.useMemo=function(e,t){return $e.current.useMemo(e,t)};Z.useReducer=function(e,t,n){return $e.current.useReducer(e,t,n)};Z.useRef=function(e){return $e.current.useRef(e)};Z.useState=function(e){return $e.current.useState(e)};Z.useSyncExternalStore=function(e,t,n){return $e.current.useSyncExternalStore(e,t,n)};Z.useTransition=function(){return $e.current.useTransition()};Z.version="18.3.1";xu.exports=Z;var N=xu.exports;const Jd=Rd(N);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ef=N,tf=Symbol.for("react.element"),nf=Symbol.for("react.fragment"),rf=Object.prototype.hasOwnProperty,lf=ef.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,sf={key:!0,ref:!0,__self:!0,__source:!0};function bu(e,t,n){var r,l={},a=null,o=null;n!==void 0&&(a=""+n),t.key!==void 0&&(a=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)rf.call(t,r)&&!sf.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)l[r]===void 0&&(l[r]=t[r]);return{$$typeof:tf,type:e,key:a,ref:o,props:l,_owner:lf.current}}ql.Fragment=nf;ql.jsx=bu;ql.jsxs=bu;gu.exports=ql;var s=gu.exports,Js={},Eu={exports:{}},qe={},Iu={exports:{}},Pu={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,Q){var U=P.length;P.push(Q);e:for(;0<U;){var ne=U-1>>>1,q=P[ne];if(0<l(q,Q))P[ne]=Q,P[U]=q,U=ne;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var Q=P[0],U=P.pop();if(U!==Q){P[0]=U;e:for(var ne=0,q=P.length,J=q>>>1;ne<J;){var V=2*(ne+1)-1,Y=P[V],je=V+1,vt=P[je];if(0>l(Y,U))je<q&&0>l(vt,Y)?(P[ne]=vt,P[je]=U,ne=je):(P[ne]=Y,P[V]=U,ne=V);else if(je<q&&0>l(vt,U))P[ne]=vt,P[je]=U,ne=je;else break e}}return Q}function l(P,Q){var U=P.sortIndex-Q.sortIndex;return U!==0?U:P.id-Q.id}if(typeof performance=="object"&&typeof performance.now=="function"){var a=performance;e.unstable_now=function(){return a.now()}}else{var o=Date,i=o.now();e.unstable_now=function(){return o.now()-i}}var u=[],c=[],g=1,x=null,y=3,j=!1,w=!1,C=!1,$=typeof setTimeout=="function"?setTimeout:null,f=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(P){for(var Q=n(c);Q!==null;){if(Q.callback===null)r(c);else if(Q.startTime<=P)r(c),Q.sortIndex=Q.expirationTime,t(u,Q);else break;Q=n(c)}}function p(P){if(C=!1,m(P),!w)if(n(u)!==null)w=!0,xe(S);else{var Q=n(c);Q!==null&&le(p,Q.startTime-P)}}function S(P,Q){w=!1,C&&(C=!1,f(_),_=-1),j=!0;var U=y;try{for(m(Q),x=n(u);x!==null&&(!(x.expirationTime>Q)||P&&!te());){var ne=x.callback;if(typeof ne=="function"){x.callback=null,y=x.priorityLevel;var q=ne(x.expirationTime<=Q);Q=e.unstable_now(),typeof q=="function"?x.callback=q:x===n(u)&&r(u),m(Q)}else r(u);x=n(u)}if(x!==null)var J=!0;else{var V=n(c);V!==null&&le(p,V.startTime-Q),J=!1}return J}finally{x=null,y=U,j=!1}}var A=!1,M=null,_=-1,R=5,T=-1;function te(){return!(e.unstable_now()-T<R)}function ve(){if(M!==null){var P=e.unstable_now();T=P;var Q=!0;try{Q=M(!0,P)}finally{Q?H():(A=!1,M=null)}}else A=!1}var H;if(typeof d=="function")H=function(){d(ve)};else if(typeof MessageChannel<"u"){var oe=new MessageChannel,we=oe.port2;oe.port1.onmessage=ve,H=function(){we.postMessage(null)}}else H=function(){$(ve,0)};function xe(P){M=P,A||(A=!0,H())}function le(P,Q){_=$(function(){P(e.unstable_now())},Q)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){w||j||(w=!0,xe(S))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):R=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return y},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(P){switch(y){case 1:case 2:case 3:var Q=3;break;default:Q=y}var U=y;y=Q;try{return P()}finally{y=U}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,Q){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var U=y;y=P;try{return Q()}finally{y=U}},e.unstable_scheduleCallback=function(P,Q,U){var ne=e.unstable_now();switch(typeof U=="object"&&U!==null?(U=U.delay,U=typeof U=="number"&&0<U?ne+U:ne):U=ne,P){case 1:var q=-1;break;case 2:q=250;break;case 5:q=**********;break;case 4:q=1e4;break;default:q=5e3}return q=U+q,P={id:g++,callback:Q,priorityLevel:P,startTime:U,expirationTime:q,sortIndex:-1},U>ne?(P.sortIndex=U,t(c,P),n(u)===null&&P===n(c)&&(C?(f(_),_=-1):C=!0,le(p,U-ne))):(P.sortIndex=q,t(u,P),w||j||(w=!0,xe(S))),P},e.unstable_shouldYield=te,e.unstable_wrapCallback=function(P){var Q=y;return function(){var U=y;y=Q;try{return P.apply(this,arguments)}finally{y=U}}}})(Pu);Iu.exports=Pu;var af=Iu.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var of=N,Ze=af;function I(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var _u=new Set,Nr={};function hn(e,t){Fn(e,t),Fn(e+"Capture",t)}function Fn(e,t){for(Nr[e]=t,e=0;e<t.length;e++)_u.add(t[e])}var bt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ea=Object.prototype.hasOwnProperty,uf=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ei={},ti={};function cf(e){return ea.call(ti,e)?!0:ea.call(ei,e)?!1:uf.test(e)?ti[e]=!0:(ei[e]=!0,!1)}function df(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function ff(e,t,n,r){if(t===null||typeof t>"u"||df(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ue(e,t,n,r,l,a,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=o}var Me={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Me[e]=new Ue(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Me[t]=new Ue(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Me[e]=new Ue(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Me[e]=new Ue(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Me[e]=new Ue(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Me[e]=new Ue(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Me[e]=new Ue(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Me[e]=new Ue(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Me[e]=new Ue(e,5,!1,e.toLowerCase(),null,!1,!1)});var Za=/[\-:]([a-z])/g;function qa(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Za,qa);Me[t]=new Ue(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Za,qa);Me[t]=new Ue(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Za,qa);Me[t]=new Ue(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Me[e]=new Ue(e,1,!1,e.toLowerCase(),null,!1,!1)});Me.xlinkHref=new Ue("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Me[e]=new Ue(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ja(e,t,n,r){var l=Me.hasOwnProperty(t)?Me[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(ff(t,n,l,r)&&(n=null),r||l===null?cf(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var _t=of.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Kr=Symbol.for("react.element"),wn=Symbol.for("react.portal"),jn=Symbol.for("react.fragment"),eo=Symbol.for("react.strict_mode"),ta=Symbol.for("react.profiler"),zu=Symbol.for("react.provider"),Au=Symbol.for("react.context"),to=Symbol.for("react.forward_ref"),na=Symbol.for("react.suspense"),ra=Symbol.for("react.suspense_list"),no=Symbol.for("react.memo"),Mt=Symbol.for("react.lazy"),Mu=Symbol.for("react.offscreen"),ni=Symbol.iterator;function qn(e){return e===null||typeof e!="object"?null:(e=ni&&e[ni]||e["@@iterator"],typeof e=="function"?e:null)}var pe=Object.assign,Es;function ar(e){if(Es===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Es=t&&t[1]||""}return`
`+Es+e}var Is=!1;function Ps(e,t){if(!e||Is)return"";Is=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var l=c.stack.split(`
`),a=r.stack.split(`
`),o=l.length-1,i=a.length-1;1<=o&&0<=i&&l[o]!==a[i];)i--;for(;1<=o&&0<=i;o--,i--)if(l[o]!==a[i]){if(o!==1||i!==1)do if(o--,i--,0>i||l[o]!==a[i]){var u=`
`+l[o].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=o&&0<=i);break}}}finally{Is=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?ar(e):""}function mf(e){switch(e.tag){case 5:return ar(e.type);case 16:return ar("Lazy");case 13:return ar("Suspense");case 19:return ar("SuspenseList");case 0:case 2:case 15:return e=Ps(e.type,!1),e;case 11:return e=Ps(e.type.render,!1),e;case 1:return e=Ps(e.type,!0),e;default:return""}}function la(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case jn:return"Fragment";case wn:return"Portal";case ta:return"Profiler";case eo:return"StrictMode";case na:return"Suspense";case ra:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Au:return(e.displayName||"Context")+".Consumer";case zu:return(e._context.displayName||"Context")+".Provider";case to:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case no:return t=e.displayName||null,t!==null?t:la(e.type)||"Memo";case Mt:t=e._payload,e=e._init;try{return la(e(t))}catch{}}return null}function pf(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return la(t);case 8:return t===eo?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Qt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Tu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function hf(e){var t=Tu(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(o){r=""+o,a.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Zr(e){e._valueTracker||(e._valueTracker=hf(e))}function Lu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Tu(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Sl(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function sa(e,t){var n=t.checked;return pe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function ri(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Qt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Du(e,t){t=t.checked,t!=null&&Ja(e,"checked",t,!1)}function aa(e,t){Du(e,t);var n=Qt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?oa(e,t.type,n):t.hasOwnProperty("defaultValue")&&oa(e,t.type,Qt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function li(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function oa(e,t,n){(t!=="number"||Sl(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var or=Array.isArray;function An(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Qt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function ia(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(I(91));return pe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function si(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(I(92));if(or(n)){if(1<n.length)throw Error(I(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Qt(n)}}function Ru(e,t){var n=Qt(t.value),r=Qt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ai(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Fu(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ua(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Fu(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var qr,Ou=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(qr=qr||document.createElement("div"),qr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=qr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function kr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var cr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},gf=["Webkit","ms","Moz","O"];Object.keys(cr).forEach(function(e){gf.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),cr[t]=cr[e]})});function $u(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||cr.hasOwnProperty(e)&&cr[e]?(""+t).trim():t+"px"}function Uu(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=$u(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var xf=pe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ca(e,t){if(t){if(xf[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(I(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(I(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(I(61))}if(t.style!=null&&typeof t.style!="object")throw Error(I(62))}}function da(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var fa=null;function ro(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ma=null,Mn=null,Tn=null;function oi(e){if(e=Hr(e)){if(typeof ma!="function")throw Error(I(280));var t=e.stateNode;t&&(t=rs(t),ma(e.stateNode,e.type,t))}}function Hu(e){Mn?Tn?Tn.push(e):Tn=[e]:Mn=e}function Bu(){if(Mn){var e=Mn,t=Tn;if(Tn=Mn=null,oi(e),t)for(e=0;e<t.length;e++)oi(t[e])}}function Vu(e,t){return e(t)}function Wu(){}var _s=!1;function Xu(e,t,n){if(_s)return e(t,n);_s=!0;try{return Vu(e,t,n)}finally{_s=!1,(Mn!==null||Tn!==null)&&(Wu(),Bu())}}function Cr(e,t){var n=e.stateNode;if(n===null)return null;var r=rs(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(I(231,t,typeof n));return n}var pa=!1;if(bt)try{var Jn={};Object.defineProperty(Jn,"passive",{get:function(){pa=!0}}),window.addEventListener("test",Jn,Jn),window.removeEventListener("test",Jn,Jn)}catch{pa=!1}function yf(e,t,n,r,l,a,o,i,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(g){this.onError(g)}}var dr=!1,bl=null,El=!1,ha=null,vf={onError:function(e){dr=!0,bl=e}};function wf(e,t,n,r,l,a,o,i,u){dr=!1,bl=null,yf.apply(vf,arguments)}function jf(e,t,n,r,l,a,o,i,u){if(wf.apply(this,arguments),dr){if(dr){var c=bl;dr=!1,bl=null}else throw Error(I(198));El||(El=!0,ha=c)}}function gn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Qu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ii(e){if(gn(e)!==e)throw Error(I(188))}function Nf(e){var t=e.alternate;if(!t){if(t=gn(e),t===null)throw Error(I(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var a=l.alternate;if(a===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===a.child){for(a=l.child;a;){if(a===n)return ii(l),e;if(a===r)return ii(l),t;a=a.sibling}throw Error(I(188))}if(n.return!==r.return)n=l,r=a;else{for(var o=!1,i=l.child;i;){if(i===n){o=!0,n=l,r=a;break}if(i===r){o=!0,r=l,n=a;break}i=i.sibling}if(!o){for(i=a.child;i;){if(i===n){o=!0,n=a,r=l;break}if(i===r){o=!0,r=a,n=l;break}i=i.sibling}if(!o)throw Error(I(189))}}if(n.alternate!==r)throw Error(I(190))}if(n.tag!==3)throw Error(I(188));return n.stateNode.current===n?e:t}function Gu(e){return e=Nf(e),e!==null?Yu(e):null}function Yu(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Yu(e);if(t!==null)return t;e=e.sibling}return null}var Ku=Ze.unstable_scheduleCallback,ui=Ze.unstable_cancelCallback,kf=Ze.unstable_shouldYield,Cf=Ze.unstable_requestPaint,ge=Ze.unstable_now,Sf=Ze.unstable_getCurrentPriorityLevel,lo=Ze.unstable_ImmediatePriority,Zu=Ze.unstable_UserBlockingPriority,Il=Ze.unstable_NormalPriority,bf=Ze.unstable_LowPriority,qu=Ze.unstable_IdlePriority,Jl=null,gt=null;function Ef(e){if(gt&&typeof gt.onCommitFiberRoot=="function")try{gt.onCommitFiberRoot(Jl,e,void 0,(e.current.flags&128)===128)}catch{}}var ct=Math.clz32?Math.clz32:_f,If=Math.log,Pf=Math.LN2;function _f(e){return e>>>=0,e===0?32:31-(If(e)/Pf|0)|0}var Jr=64,el=4194304;function ir(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Pl(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,a=e.pingedLanes,o=n&268435455;if(o!==0){var i=o&~l;i!==0?r=ir(i):(a&=o,a!==0&&(r=ir(a)))}else o=n&~l,o!==0?r=ir(o):a!==0&&(r=ir(a));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,a=t&-t,l>=a||l===16&&(a&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-ct(t),l=1<<n,r|=e[n],t&=~l;return r}function zf(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Af(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,a=e.pendingLanes;0<a;){var o=31-ct(a),i=1<<o,u=l[o];u===-1?(!(i&n)||i&r)&&(l[o]=zf(i,t)):u<=t&&(e.expiredLanes|=i),a&=~i}}function ga(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Ju(){var e=Jr;return Jr<<=1,!(Jr&4194240)&&(Jr=64),e}function zs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function $r(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ct(t),e[t]=n}function Mf(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-ct(n),a=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~a}}function so(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ct(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var se=0;function ec(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var tc,ao,nc,rc,lc,xa=!1,tl=[],Ot=null,$t=null,Ut=null,Sr=new Map,br=new Map,Lt=[],Tf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ci(e,t){switch(e){case"focusin":case"focusout":Ot=null;break;case"dragenter":case"dragleave":$t=null;break;case"mouseover":case"mouseout":Ut=null;break;case"pointerover":case"pointerout":Sr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":br.delete(t.pointerId)}}function er(e,t,n,r,l,a){return e===null||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[l]},t!==null&&(t=Hr(t),t!==null&&ao(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Lf(e,t,n,r,l){switch(t){case"focusin":return Ot=er(Ot,e,t,n,r,l),!0;case"dragenter":return $t=er($t,e,t,n,r,l),!0;case"mouseover":return Ut=er(Ut,e,t,n,r,l),!0;case"pointerover":var a=l.pointerId;return Sr.set(a,er(Sr.get(a)||null,e,t,n,r,l)),!0;case"gotpointercapture":return a=l.pointerId,br.set(a,er(br.get(a)||null,e,t,n,r,l)),!0}return!1}function sc(e){var t=rn(e.target);if(t!==null){var n=gn(t);if(n!==null){if(t=n.tag,t===13){if(t=Qu(n),t!==null){e.blockedOn=t,lc(e.priority,function(){nc(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function hl(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ya(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);fa=r,n.target.dispatchEvent(r),fa=null}else return t=Hr(n),t!==null&&ao(t),e.blockedOn=n,!1;t.shift()}return!0}function di(e,t,n){hl(e)&&n.delete(t)}function Df(){xa=!1,Ot!==null&&hl(Ot)&&(Ot=null),$t!==null&&hl($t)&&($t=null),Ut!==null&&hl(Ut)&&(Ut=null),Sr.forEach(di),br.forEach(di)}function tr(e,t){e.blockedOn===t&&(e.blockedOn=null,xa||(xa=!0,Ze.unstable_scheduleCallback(Ze.unstable_NormalPriority,Df)))}function Er(e){function t(l){return tr(l,e)}if(0<tl.length){tr(tl[0],e);for(var n=1;n<tl.length;n++){var r=tl[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Ot!==null&&tr(Ot,e),$t!==null&&tr($t,e),Ut!==null&&tr(Ut,e),Sr.forEach(t),br.forEach(t),n=0;n<Lt.length;n++)r=Lt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Lt.length&&(n=Lt[0],n.blockedOn===null);)sc(n),n.blockedOn===null&&Lt.shift()}var Ln=_t.ReactCurrentBatchConfig,_l=!0;function Rf(e,t,n,r){var l=se,a=Ln.transition;Ln.transition=null;try{se=1,oo(e,t,n,r)}finally{se=l,Ln.transition=a}}function Ff(e,t,n,r){var l=se,a=Ln.transition;Ln.transition=null;try{se=4,oo(e,t,n,r)}finally{se=l,Ln.transition=a}}function oo(e,t,n,r){if(_l){var l=ya(e,t,n,r);if(l===null)Us(e,t,r,zl,n),ci(e,r);else if(Lf(l,e,t,n,r))r.stopPropagation();else if(ci(e,r),t&4&&-1<Tf.indexOf(e)){for(;l!==null;){var a=Hr(l);if(a!==null&&tc(a),a=ya(e,t,n,r),a===null&&Us(e,t,r,zl,n),a===l)break;l=a}l!==null&&r.stopPropagation()}else Us(e,t,r,null,n)}}var zl=null;function ya(e,t,n,r){if(zl=null,e=ro(r),e=rn(e),e!==null)if(t=gn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Qu(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return zl=e,null}function ac(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Sf()){case lo:return 1;case Zu:return 4;case Il:case bf:return 16;case qu:return 536870912;default:return 16}default:return 16}}var Rt=null,io=null,gl=null;function oc(){if(gl)return gl;var e,t=io,n=t.length,r,l="value"in Rt?Rt.value:Rt.textContent,a=l.length;for(e=0;e<n&&t[e]===l[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===l[a-r];r++);return gl=l.slice(e,1<r?1-r:void 0)}function xl(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function nl(){return!0}function fi(){return!1}function Je(e){function t(n,r,l,a,o){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=a,this.target=o,this.currentTarget=null;for(var i in e)e.hasOwnProperty(i)&&(n=e[i],this[i]=n?n(a):a[i]);return this.isDefaultPrevented=(a.defaultPrevented!=null?a.defaultPrevented:a.returnValue===!1)?nl:fi,this.isPropagationStopped=fi,this}return pe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=nl)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=nl)},persist:function(){},isPersistent:nl}),t}var Xn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},uo=Je(Xn),Ur=pe({},Xn,{view:0,detail:0}),Of=Je(Ur),As,Ms,nr,es=pe({},Ur,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:co,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==nr&&(nr&&e.type==="mousemove"?(As=e.screenX-nr.screenX,Ms=e.screenY-nr.screenY):Ms=As=0,nr=e),As)},movementY:function(e){return"movementY"in e?e.movementY:Ms}}),mi=Je(es),$f=pe({},es,{dataTransfer:0}),Uf=Je($f),Hf=pe({},Ur,{relatedTarget:0}),Ts=Je(Hf),Bf=pe({},Xn,{animationName:0,elapsedTime:0,pseudoElement:0}),Vf=Je(Bf),Wf=pe({},Xn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Xf=Je(Wf),Qf=pe({},Xn,{data:0}),pi=Je(Qf),Gf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Yf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Kf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Zf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Kf[e])?!!t[e]:!1}function co(){return Zf}var qf=pe({},Ur,{key:function(e){if(e.key){var t=Gf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=xl(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Yf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:co,charCode:function(e){return e.type==="keypress"?xl(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?xl(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Jf=Je(qf),em=pe({},es,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),hi=Je(em),tm=pe({},Ur,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:co}),nm=Je(tm),rm=pe({},Xn,{propertyName:0,elapsedTime:0,pseudoElement:0}),lm=Je(rm),sm=pe({},es,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),am=Je(sm),om=[9,13,27,32],fo=bt&&"CompositionEvent"in window,fr=null;bt&&"documentMode"in document&&(fr=document.documentMode);var im=bt&&"TextEvent"in window&&!fr,ic=bt&&(!fo||fr&&8<fr&&11>=fr),gi=" ",xi=!1;function uc(e,t){switch(e){case"keyup":return om.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function cc(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Nn=!1;function um(e,t){switch(e){case"compositionend":return cc(t);case"keypress":return t.which!==32?null:(xi=!0,gi);case"textInput":return e=t.data,e===gi&&xi?null:e;default:return null}}function cm(e,t){if(Nn)return e==="compositionend"||!fo&&uc(e,t)?(e=oc(),gl=io=Rt=null,Nn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ic&&t.locale!=="ko"?null:t.data;default:return null}}var dm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!dm[e.type]:t==="textarea"}function dc(e,t,n,r){Hu(r),t=Al(t,"onChange"),0<t.length&&(n=new uo("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var mr=null,Ir=null;function fm(e){Nc(e,0)}function ts(e){var t=Sn(e);if(Lu(t))return e}function mm(e,t){if(e==="change")return t}var fc=!1;if(bt){var Ls;if(bt){var Ds="oninput"in document;if(!Ds){var vi=document.createElement("div");vi.setAttribute("oninput","return;"),Ds=typeof vi.oninput=="function"}Ls=Ds}else Ls=!1;fc=Ls&&(!document.documentMode||9<document.documentMode)}function wi(){mr&&(mr.detachEvent("onpropertychange",mc),Ir=mr=null)}function mc(e){if(e.propertyName==="value"&&ts(Ir)){var t=[];dc(t,Ir,e,ro(e)),Xu(fm,t)}}function pm(e,t,n){e==="focusin"?(wi(),mr=t,Ir=n,mr.attachEvent("onpropertychange",mc)):e==="focusout"&&wi()}function hm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ts(Ir)}function gm(e,t){if(e==="click")return ts(t)}function xm(e,t){if(e==="input"||e==="change")return ts(t)}function ym(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ft=typeof Object.is=="function"?Object.is:ym;function Pr(e,t){if(ft(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!ea.call(t,l)||!ft(e[l],t[l]))return!1}return!0}function ji(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ni(e,t){var n=ji(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=ji(n)}}function pc(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?pc(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function hc(){for(var e=window,t=Sl();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Sl(e.document)}return t}function mo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function vm(e){var t=hc(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&pc(n.ownerDocument.documentElement,n)){if(r!==null&&mo(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,a=Math.min(r.start,l);r=r.end===void 0?a:Math.min(r.end,l),!e.extend&&a>r&&(l=r,r=a,a=l),l=Ni(n,a);var o=Ni(n,r);l&&o&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),a>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var wm=bt&&"documentMode"in document&&11>=document.documentMode,kn=null,va=null,pr=null,wa=!1;function ki(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;wa||kn==null||kn!==Sl(r)||(r=kn,"selectionStart"in r&&mo(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),pr&&Pr(pr,r)||(pr=r,r=Al(va,"onSelect"),0<r.length&&(t=new uo("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=kn)))}function rl(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Cn={animationend:rl("Animation","AnimationEnd"),animationiteration:rl("Animation","AnimationIteration"),animationstart:rl("Animation","AnimationStart"),transitionend:rl("Transition","TransitionEnd")},Rs={},gc={};bt&&(gc=document.createElement("div").style,"AnimationEvent"in window||(delete Cn.animationend.animation,delete Cn.animationiteration.animation,delete Cn.animationstart.animation),"TransitionEvent"in window||delete Cn.transitionend.transition);function ns(e){if(Rs[e])return Rs[e];if(!Cn[e])return e;var t=Cn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in gc)return Rs[e]=t[n];return e}var xc=ns("animationend"),yc=ns("animationiteration"),vc=ns("animationstart"),wc=ns("transitionend"),jc=new Map,Ci="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Yt(e,t){jc.set(e,t),hn(t,[e])}for(var Fs=0;Fs<Ci.length;Fs++){var Os=Ci[Fs],jm=Os.toLowerCase(),Nm=Os[0].toUpperCase()+Os.slice(1);Yt(jm,"on"+Nm)}Yt(xc,"onAnimationEnd");Yt(yc,"onAnimationIteration");Yt(vc,"onAnimationStart");Yt("dblclick","onDoubleClick");Yt("focusin","onFocus");Yt("focusout","onBlur");Yt(wc,"onTransitionEnd");Fn("onMouseEnter",["mouseout","mouseover"]);Fn("onMouseLeave",["mouseout","mouseover"]);Fn("onPointerEnter",["pointerout","pointerover"]);Fn("onPointerLeave",["pointerout","pointerover"]);hn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));hn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));hn("onBeforeInput",["compositionend","keypress","textInput","paste"]);hn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));hn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));hn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ur="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),km=new Set("cancel close invalid load scroll toggle".split(" ").concat(ur));function Si(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,jf(r,t,void 0,e),e.currentTarget=null}function Nc(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var o=r.length-1;0<=o;o--){var i=r[o],u=i.instance,c=i.currentTarget;if(i=i.listener,u!==a&&l.isPropagationStopped())break e;Si(l,i,c),a=u}else for(o=0;o<r.length;o++){if(i=r[o],u=i.instance,c=i.currentTarget,i=i.listener,u!==a&&l.isPropagationStopped())break e;Si(l,i,c),a=u}}}if(El)throw e=ha,El=!1,ha=null,e}function ue(e,t){var n=t[Sa];n===void 0&&(n=t[Sa]=new Set);var r=e+"__bubble";n.has(r)||(kc(t,e,2,!1),n.add(r))}function $s(e,t,n){var r=0;t&&(r|=4),kc(n,e,r,t)}var ll="_reactListening"+Math.random().toString(36).slice(2);function _r(e){if(!e[ll]){e[ll]=!0,_u.forEach(function(n){n!=="selectionchange"&&(km.has(n)||$s(n,!1,e),$s(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ll]||(t[ll]=!0,$s("selectionchange",!1,t))}}function kc(e,t,n,r){switch(ac(t)){case 1:var l=Rf;break;case 4:l=Ff;break;default:l=oo}n=l.bind(null,t,n,e),l=void 0,!pa||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function Us(e,t,n,r,l){var a=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var i=r.stateNode.containerInfo;if(i===l||i.nodeType===8&&i.parentNode===l)break;if(o===4)for(o=r.return;o!==null;){var u=o.tag;if((u===3||u===4)&&(u=o.stateNode.containerInfo,u===l||u.nodeType===8&&u.parentNode===l))return;o=o.return}for(;i!==null;){if(o=rn(i),o===null)return;if(u=o.tag,u===5||u===6){r=a=o;continue e}i=i.parentNode}}r=r.return}Xu(function(){var c=a,g=ro(n),x=[];e:{var y=jc.get(e);if(y!==void 0){var j=uo,w=e;switch(e){case"keypress":if(xl(n)===0)break e;case"keydown":case"keyup":j=Jf;break;case"focusin":w="focus",j=Ts;break;case"focusout":w="blur",j=Ts;break;case"beforeblur":case"afterblur":j=Ts;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":j=mi;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":j=Uf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":j=nm;break;case xc:case yc:case vc:j=Vf;break;case wc:j=lm;break;case"scroll":j=Of;break;case"wheel":j=am;break;case"copy":case"cut":case"paste":j=Xf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":j=hi}var C=(t&4)!==0,$=!C&&e==="scroll",f=C?y!==null?y+"Capture":null:y;C=[];for(var d=c,m;d!==null;){m=d;var p=m.stateNode;if(m.tag===5&&p!==null&&(m=p,f!==null&&(p=Cr(d,f),p!=null&&C.push(zr(d,p,m)))),$)break;d=d.return}0<C.length&&(y=new j(y,w,null,n,g),x.push({event:y,listeners:C}))}}if(!(t&7)){e:{if(y=e==="mouseover"||e==="pointerover",j=e==="mouseout"||e==="pointerout",y&&n!==fa&&(w=n.relatedTarget||n.fromElement)&&(rn(w)||w[Et]))break e;if((j||y)&&(y=g.window===g?g:(y=g.ownerDocument)?y.defaultView||y.parentWindow:window,j?(w=n.relatedTarget||n.toElement,j=c,w=w?rn(w):null,w!==null&&($=gn(w),w!==$||w.tag!==5&&w.tag!==6)&&(w=null)):(j=null,w=c),j!==w)){if(C=mi,p="onMouseLeave",f="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(C=hi,p="onPointerLeave",f="onPointerEnter",d="pointer"),$=j==null?y:Sn(j),m=w==null?y:Sn(w),y=new C(p,d+"leave",j,n,g),y.target=$,y.relatedTarget=m,p=null,rn(g)===c&&(C=new C(f,d+"enter",w,n,g),C.target=m,C.relatedTarget=$,p=C),$=p,j&&w)t:{for(C=j,f=w,d=0,m=C;m;m=yn(m))d++;for(m=0,p=f;p;p=yn(p))m++;for(;0<d-m;)C=yn(C),d--;for(;0<m-d;)f=yn(f),m--;for(;d--;){if(C===f||f!==null&&C===f.alternate)break t;C=yn(C),f=yn(f)}C=null}else C=null;j!==null&&bi(x,y,j,C,!1),w!==null&&$!==null&&bi(x,$,w,C,!0)}}e:{if(y=c?Sn(c):window,j=y.nodeName&&y.nodeName.toLowerCase(),j==="select"||j==="input"&&y.type==="file")var S=mm;else if(yi(y))if(fc)S=xm;else{S=hm;var A=pm}else(j=y.nodeName)&&j.toLowerCase()==="input"&&(y.type==="checkbox"||y.type==="radio")&&(S=gm);if(S&&(S=S(e,c))){dc(x,S,n,g);break e}A&&A(e,y,c),e==="focusout"&&(A=y._wrapperState)&&A.controlled&&y.type==="number"&&oa(y,"number",y.value)}switch(A=c?Sn(c):window,e){case"focusin":(yi(A)||A.contentEditable==="true")&&(kn=A,va=c,pr=null);break;case"focusout":pr=va=kn=null;break;case"mousedown":wa=!0;break;case"contextmenu":case"mouseup":case"dragend":wa=!1,ki(x,n,g);break;case"selectionchange":if(wm)break;case"keydown":case"keyup":ki(x,n,g)}var M;if(fo)e:{switch(e){case"compositionstart":var _="onCompositionStart";break e;case"compositionend":_="onCompositionEnd";break e;case"compositionupdate":_="onCompositionUpdate";break e}_=void 0}else Nn?uc(e,n)&&(_="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(_="onCompositionStart");_&&(ic&&n.locale!=="ko"&&(Nn||_!=="onCompositionStart"?_==="onCompositionEnd"&&Nn&&(M=oc()):(Rt=g,io="value"in Rt?Rt.value:Rt.textContent,Nn=!0)),A=Al(c,_),0<A.length&&(_=new pi(_,e,null,n,g),x.push({event:_,listeners:A}),M?_.data=M:(M=cc(n),M!==null&&(_.data=M)))),(M=im?um(e,n):cm(e,n))&&(c=Al(c,"onBeforeInput"),0<c.length&&(g=new pi("onBeforeInput","beforeinput",null,n,g),x.push({event:g,listeners:c}),g.data=M))}Nc(x,t)})}function zr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Al(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,a=l.stateNode;l.tag===5&&a!==null&&(l=a,a=Cr(e,n),a!=null&&r.unshift(zr(e,a,l)),a=Cr(e,t),a!=null&&r.push(zr(e,a,l))),e=e.return}return r}function yn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function bi(e,t,n,r,l){for(var a=t._reactName,o=[];n!==null&&n!==r;){var i=n,u=i.alternate,c=i.stateNode;if(u!==null&&u===r)break;i.tag===5&&c!==null&&(i=c,l?(u=Cr(n,a),u!=null&&o.unshift(zr(n,u,i))):l||(u=Cr(n,a),u!=null&&o.push(zr(n,u,i)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Cm=/\r\n?/g,Sm=/\u0000|\uFFFD/g;function Ei(e){return(typeof e=="string"?e:""+e).replace(Cm,`
`).replace(Sm,"")}function sl(e,t,n){if(t=Ei(t),Ei(e)!==t&&n)throw Error(I(425))}function Ml(){}var ja=null,Na=null;function ka(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ca=typeof setTimeout=="function"?setTimeout:void 0,bm=typeof clearTimeout=="function"?clearTimeout:void 0,Ii=typeof Promise=="function"?Promise:void 0,Em=typeof queueMicrotask=="function"?queueMicrotask:typeof Ii<"u"?function(e){return Ii.resolve(null).then(e).catch(Im)}:Ca;function Im(e){setTimeout(function(){throw e})}function Hs(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),Er(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);Er(t)}function Ht(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Pi(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Qn=Math.random().toString(36).slice(2),ht="__reactFiber$"+Qn,Ar="__reactProps$"+Qn,Et="__reactContainer$"+Qn,Sa="__reactEvents$"+Qn,Pm="__reactListeners$"+Qn,_m="__reactHandles$"+Qn;function rn(e){var t=e[ht];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Et]||n[ht]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Pi(e);e!==null;){if(n=e[ht])return n;e=Pi(e)}return t}e=n,n=e.parentNode}return null}function Hr(e){return e=e[ht]||e[Et],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Sn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(I(33))}function rs(e){return e[Ar]||null}var ba=[],bn=-1;function Kt(e){return{current:e}}function ce(e){0>bn||(e.current=ba[bn],ba[bn]=null,bn--)}function ae(e,t){bn++,ba[bn]=e.current,e.current=t}var Gt={},Re=Kt(Gt),We=Kt(!1),un=Gt;function On(e,t){var n=e.type.contextTypes;if(!n)return Gt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},a;for(a in n)l[a]=t[a];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Xe(e){return e=e.childContextTypes,e!=null}function Tl(){ce(We),ce(Re)}function _i(e,t,n){if(Re.current!==Gt)throw Error(I(168));ae(Re,t),ae(We,n)}function Cc(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(I(108,pf(e)||"Unknown",l));return pe({},n,r)}function Ll(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Gt,un=Re.current,ae(Re,e),ae(We,We.current),!0}function zi(e,t,n){var r=e.stateNode;if(!r)throw Error(I(169));n?(e=Cc(e,t,un),r.__reactInternalMemoizedMergedChildContext=e,ce(We),ce(Re),ae(Re,e)):ce(We),ae(We,n)}var Nt=null,ls=!1,Bs=!1;function Sc(e){Nt===null?Nt=[e]:Nt.push(e)}function zm(e){ls=!0,Sc(e)}function Zt(){if(!Bs&&Nt!==null){Bs=!0;var e=0,t=se;try{var n=Nt;for(se=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Nt=null,ls=!1}catch(l){throw Nt!==null&&(Nt=Nt.slice(e+1)),Ku(lo,Zt),l}finally{se=t,Bs=!1}}return null}var En=[],In=0,Dl=null,Rl=0,et=[],tt=0,cn=null,kt=1,Ct="";function tn(e,t){En[In++]=Rl,En[In++]=Dl,Dl=e,Rl=t}function bc(e,t,n){et[tt++]=kt,et[tt++]=Ct,et[tt++]=cn,cn=e;var r=kt;e=Ct;var l=32-ct(r)-1;r&=~(1<<l),n+=1;var a=32-ct(t)+l;if(30<a){var o=l-l%5;a=(r&(1<<o)-1).toString(32),r>>=o,l-=o,kt=1<<32-ct(t)+l|n<<l|r,Ct=a+e}else kt=1<<a|n<<l|r,Ct=e}function po(e){e.return!==null&&(tn(e,1),bc(e,1,0))}function ho(e){for(;e===Dl;)Dl=En[--In],En[In]=null,Rl=En[--In],En[In]=null;for(;e===cn;)cn=et[--tt],et[tt]=null,Ct=et[--tt],et[tt]=null,kt=et[--tt],et[tt]=null}var Ke=null,Ye=null,de=!1,ut=null;function Ec(e,t){var n=nt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Ai(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ke=e,Ye=Ht(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ke=e,Ye=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=cn!==null?{id:kt,overflow:Ct}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=nt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ke=e,Ye=null,!0):!1;default:return!1}}function Ea(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ia(e){if(de){var t=Ye;if(t){var n=t;if(!Ai(e,t)){if(Ea(e))throw Error(I(418));t=Ht(n.nextSibling);var r=Ke;t&&Ai(e,t)?Ec(r,n):(e.flags=e.flags&-4097|2,de=!1,Ke=e)}}else{if(Ea(e))throw Error(I(418));e.flags=e.flags&-4097|2,de=!1,Ke=e}}}function Mi(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ke=e}function al(e){if(e!==Ke)return!1;if(!de)return Mi(e),de=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ka(e.type,e.memoizedProps)),t&&(t=Ye)){if(Ea(e))throw Ic(),Error(I(418));for(;t;)Ec(e,t),t=Ht(t.nextSibling)}if(Mi(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(I(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ye=Ht(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ye=null}}else Ye=Ke?Ht(e.stateNode.nextSibling):null;return!0}function Ic(){for(var e=Ye;e;)e=Ht(e.nextSibling)}function $n(){Ye=Ke=null,de=!1}function go(e){ut===null?ut=[e]:ut.push(e)}var Am=_t.ReactCurrentBatchConfig;function rr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(I(309));var r=n.stateNode}if(!r)throw Error(I(147,e));var l=r,a=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===a?t.ref:(t=function(o){var i=l.refs;o===null?delete i[a]:i[a]=o},t._stringRef=a,t)}if(typeof e!="string")throw Error(I(284));if(!n._owner)throw Error(I(290,e))}return e}function ol(e,t){throw e=Object.prototype.toString.call(t),Error(I(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ti(e){var t=e._init;return t(e._payload)}function Pc(e){function t(f,d){if(e){var m=f.deletions;m===null?(f.deletions=[d],f.flags|=16):m.push(d)}}function n(f,d){if(!e)return null;for(;d!==null;)t(f,d),d=d.sibling;return null}function r(f,d){for(f=new Map;d!==null;)d.key!==null?f.set(d.key,d):f.set(d.index,d),d=d.sibling;return f}function l(f,d){return f=Xt(f,d),f.index=0,f.sibling=null,f}function a(f,d,m){return f.index=m,e?(m=f.alternate,m!==null?(m=m.index,m<d?(f.flags|=2,d):m):(f.flags|=2,d)):(f.flags|=1048576,d)}function o(f){return e&&f.alternate===null&&(f.flags|=2),f}function i(f,d,m,p){return d===null||d.tag!==6?(d=Ks(m,f.mode,p),d.return=f,d):(d=l(d,m),d.return=f,d)}function u(f,d,m,p){var S=m.type;return S===jn?g(f,d,m.props.children,p,m.key):d!==null&&(d.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Mt&&Ti(S)===d.type)?(p=l(d,m.props),p.ref=rr(f,d,m),p.return=f,p):(p=Cl(m.type,m.key,m.props,null,f.mode,p),p.ref=rr(f,d,m),p.return=f,p)}function c(f,d,m,p){return d===null||d.tag!==4||d.stateNode.containerInfo!==m.containerInfo||d.stateNode.implementation!==m.implementation?(d=Zs(m,f.mode,p),d.return=f,d):(d=l(d,m.children||[]),d.return=f,d)}function g(f,d,m,p,S){return d===null||d.tag!==7?(d=on(m,f.mode,p,S),d.return=f,d):(d=l(d,m),d.return=f,d)}function x(f,d,m){if(typeof d=="string"&&d!==""||typeof d=="number")return d=Ks(""+d,f.mode,m),d.return=f,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Kr:return m=Cl(d.type,d.key,d.props,null,f.mode,m),m.ref=rr(f,null,d),m.return=f,m;case wn:return d=Zs(d,f.mode,m),d.return=f,d;case Mt:var p=d._init;return x(f,p(d._payload),m)}if(or(d)||qn(d))return d=on(d,f.mode,m,null),d.return=f,d;ol(f,d)}return null}function y(f,d,m,p){var S=d!==null?d.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return S!==null?null:i(f,d,""+m,p);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Kr:return m.key===S?u(f,d,m,p):null;case wn:return m.key===S?c(f,d,m,p):null;case Mt:return S=m._init,y(f,d,S(m._payload),p)}if(or(m)||qn(m))return S!==null?null:g(f,d,m,p,null);ol(f,m)}return null}function j(f,d,m,p,S){if(typeof p=="string"&&p!==""||typeof p=="number")return f=f.get(m)||null,i(d,f,""+p,S);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Kr:return f=f.get(p.key===null?m:p.key)||null,u(d,f,p,S);case wn:return f=f.get(p.key===null?m:p.key)||null,c(d,f,p,S);case Mt:var A=p._init;return j(f,d,m,A(p._payload),S)}if(or(p)||qn(p))return f=f.get(m)||null,g(d,f,p,S,null);ol(d,p)}return null}function w(f,d,m,p){for(var S=null,A=null,M=d,_=d=0,R=null;M!==null&&_<m.length;_++){M.index>_?(R=M,M=null):R=M.sibling;var T=y(f,M,m[_],p);if(T===null){M===null&&(M=R);break}e&&M&&T.alternate===null&&t(f,M),d=a(T,d,_),A===null?S=T:A.sibling=T,A=T,M=R}if(_===m.length)return n(f,M),de&&tn(f,_),S;if(M===null){for(;_<m.length;_++)M=x(f,m[_],p),M!==null&&(d=a(M,d,_),A===null?S=M:A.sibling=M,A=M);return de&&tn(f,_),S}for(M=r(f,M);_<m.length;_++)R=j(M,f,_,m[_],p),R!==null&&(e&&R.alternate!==null&&M.delete(R.key===null?_:R.key),d=a(R,d,_),A===null?S=R:A.sibling=R,A=R);return e&&M.forEach(function(te){return t(f,te)}),de&&tn(f,_),S}function C(f,d,m,p){var S=qn(m);if(typeof S!="function")throw Error(I(150));if(m=S.call(m),m==null)throw Error(I(151));for(var A=S=null,M=d,_=d=0,R=null,T=m.next();M!==null&&!T.done;_++,T=m.next()){M.index>_?(R=M,M=null):R=M.sibling;var te=y(f,M,T.value,p);if(te===null){M===null&&(M=R);break}e&&M&&te.alternate===null&&t(f,M),d=a(te,d,_),A===null?S=te:A.sibling=te,A=te,M=R}if(T.done)return n(f,M),de&&tn(f,_),S;if(M===null){for(;!T.done;_++,T=m.next())T=x(f,T.value,p),T!==null&&(d=a(T,d,_),A===null?S=T:A.sibling=T,A=T);return de&&tn(f,_),S}for(M=r(f,M);!T.done;_++,T=m.next())T=j(M,f,_,T.value,p),T!==null&&(e&&T.alternate!==null&&M.delete(T.key===null?_:T.key),d=a(T,d,_),A===null?S=T:A.sibling=T,A=T);return e&&M.forEach(function(ve){return t(f,ve)}),de&&tn(f,_),S}function $(f,d,m,p){if(typeof m=="object"&&m!==null&&m.type===jn&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case Kr:e:{for(var S=m.key,A=d;A!==null;){if(A.key===S){if(S=m.type,S===jn){if(A.tag===7){n(f,A.sibling),d=l(A,m.props.children),d.return=f,f=d;break e}}else if(A.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Mt&&Ti(S)===A.type){n(f,A.sibling),d=l(A,m.props),d.ref=rr(f,A,m),d.return=f,f=d;break e}n(f,A);break}else t(f,A);A=A.sibling}m.type===jn?(d=on(m.props.children,f.mode,p,m.key),d.return=f,f=d):(p=Cl(m.type,m.key,m.props,null,f.mode,p),p.ref=rr(f,d,m),p.return=f,f=p)}return o(f);case wn:e:{for(A=m.key;d!==null;){if(d.key===A)if(d.tag===4&&d.stateNode.containerInfo===m.containerInfo&&d.stateNode.implementation===m.implementation){n(f,d.sibling),d=l(d,m.children||[]),d.return=f,f=d;break e}else{n(f,d);break}else t(f,d);d=d.sibling}d=Zs(m,f.mode,p),d.return=f,f=d}return o(f);case Mt:return A=m._init,$(f,d,A(m._payload),p)}if(or(m))return w(f,d,m,p);if(qn(m))return C(f,d,m,p);ol(f,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,d!==null&&d.tag===6?(n(f,d.sibling),d=l(d,m),d.return=f,f=d):(n(f,d),d=Ks(m,f.mode,p),d.return=f,f=d),o(f)):n(f,d)}return $}var Un=Pc(!0),_c=Pc(!1),Fl=Kt(null),Ol=null,Pn=null,xo=null;function yo(){xo=Pn=Ol=null}function vo(e){var t=Fl.current;ce(Fl),e._currentValue=t}function Pa(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Dn(e,t){Ol=e,xo=Pn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ve=!0),e.firstContext=null)}function lt(e){var t=e._currentValue;if(xo!==e)if(e={context:e,memoizedValue:t,next:null},Pn===null){if(Ol===null)throw Error(I(308));Pn=e,Ol.dependencies={lanes:0,firstContext:e}}else Pn=Pn.next=e;return t}var ln=null;function wo(e){ln===null?ln=[e]:ln.push(e)}function zc(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,wo(t)):(n.next=l.next,l.next=n),t.interleaved=n,It(e,r)}function It(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Tt=!1;function jo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ac(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function St(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Bt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,re&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,It(e,n)}return l=r.interleaved,l===null?(t.next=t,wo(r)):(t.next=l.next,l.next=t),r.interleaved=t,It(e,n)}function yl(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,so(e,n)}}function Li(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,a=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};a===null?l=a=o:a=a.next=o,n=n.next}while(n!==null);a===null?l=a=t:a=a.next=t}else l=a=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:a,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function $l(e,t,n,r){var l=e.updateQueue;Tt=!1;var a=l.firstBaseUpdate,o=l.lastBaseUpdate,i=l.shared.pending;if(i!==null){l.shared.pending=null;var u=i,c=u.next;u.next=null,o===null?a=c:o.next=c,o=u;var g=e.alternate;g!==null&&(g=g.updateQueue,i=g.lastBaseUpdate,i!==o&&(i===null?g.firstBaseUpdate=c:i.next=c,g.lastBaseUpdate=u))}if(a!==null){var x=l.baseState;o=0,g=c=u=null,i=a;do{var y=i.lane,j=i.eventTime;if((r&y)===y){g!==null&&(g=g.next={eventTime:j,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var w=e,C=i;switch(y=t,j=n,C.tag){case 1:if(w=C.payload,typeof w=="function"){x=w.call(j,x,y);break e}x=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=C.payload,y=typeof w=="function"?w.call(j,x,y):w,y==null)break e;x=pe({},x,y);break e;case 2:Tt=!0}}i.callback!==null&&i.lane!==0&&(e.flags|=64,y=l.effects,y===null?l.effects=[i]:y.push(i))}else j={eventTime:j,lane:y,tag:i.tag,payload:i.payload,callback:i.callback,next:null},g===null?(c=g=j,u=x):g=g.next=j,o|=y;if(i=i.next,i===null){if(i=l.shared.pending,i===null)break;y=i,i=y.next,y.next=null,l.lastBaseUpdate=y,l.shared.pending=null}}while(!0);if(g===null&&(u=x),l.baseState=u,l.firstBaseUpdate=c,l.lastBaseUpdate=g,t=l.shared.interleaved,t!==null){l=t;do o|=l.lane,l=l.next;while(l!==t)}else a===null&&(l.shared.lanes=0);fn|=o,e.lanes=o,e.memoizedState=x}}function Di(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(I(191,l));l.call(r)}}}var Br={},xt=Kt(Br),Mr=Kt(Br),Tr=Kt(Br);function sn(e){if(e===Br)throw Error(I(174));return e}function No(e,t){switch(ae(Tr,t),ae(Mr,e),ae(xt,Br),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ua(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ua(t,e)}ce(xt),ae(xt,t)}function Hn(){ce(xt),ce(Mr),ce(Tr)}function Mc(e){sn(Tr.current);var t=sn(xt.current),n=ua(t,e.type);t!==n&&(ae(Mr,e),ae(xt,n))}function ko(e){Mr.current===e&&(ce(xt),ce(Mr))}var fe=Kt(0);function Ul(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Vs=[];function Co(){for(var e=0;e<Vs.length;e++)Vs[e]._workInProgressVersionPrimary=null;Vs.length=0}var vl=_t.ReactCurrentDispatcher,Ws=_t.ReactCurrentBatchConfig,dn=0,me=null,Ce=null,Ee=null,Hl=!1,hr=!1,Lr=0,Mm=0;function Te(){throw Error(I(321))}function So(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ft(e[n],t[n]))return!1;return!0}function bo(e,t,n,r,l,a){if(dn=a,me=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,vl.current=e===null||e.memoizedState===null?Rm:Fm,e=n(r,l),hr){a=0;do{if(hr=!1,Lr=0,25<=a)throw Error(I(301));a+=1,Ee=Ce=null,t.updateQueue=null,vl.current=Om,e=n(r,l)}while(hr)}if(vl.current=Bl,t=Ce!==null&&Ce.next!==null,dn=0,Ee=Ce=me=null,Hl=!1,t)throw Error(I(300));return e}function Eo(){var e=Lr!==0;return Lr=0,e}function pt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ee===null?me.memoizedState=Ee=e:Ee=Ee.next=e,Ee}function st(){if(Ce===null){var e=me.alternate;e=e!==null?e.memoizedState:null}else e=Ce.next;var t=Ee===null?me.memoizedState:Ee.next;if(t!==null)Ee=t,Ce=e;else{if(e===null)throw Error(I(310));Ce=e,e={memoizedState:Ce.memoizedState,baseState:Ce.baseState,baseQueue:Ce.baseQueue,queue:Ce.queue,next:null},Ee===null?me.memoizedState=Ee=e:Ee=Ee.next=e}return Ee}function Dr(e,t){return typeof t=="function"?t(e):t}function Xs(e){var t=st(),n=t.queue;if(n===null)throw Error(I(311));n.lastRenderedReducer=e;var r=Ce,l=r.baseQueue,a=n.pending;if(a!==null){if(l!==null){var o=l.next;l.next=a.next,a.next=o}r.baseQueue=l=a,n.pending=null}if(l!==null){a=l.next,r=r.baseState;var i=o=null,u=null,c=a;do{var g=c.lane;if((dn&g)===g)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var x={lane:g,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(i=u=x,o=r):u=u.next=x,me.lanes|=g,fn|=g}c=c.next}while(c!==null&&c!==a);u===null?o=r:u.next=i,ft(r,t.memoizedState)||(Ve=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do a=l.lane,me.lanes|=a,fn|=a,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Qs(e){var t=st(),n=t.queue;if(n===null)throw Error(I(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,a=t.memoizedState;if(l!==null){n.pending=null;var o=l=l.next;do a=e(a,o.action),o=o.next;while(o!==l);ft(a,t.memoizedState)||(Ve=!0),t.memoizedState=a,t.baseQueue===null&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function Tc(){}function Lc(e,t){var n=me,r=st(),l=t(),a=!ft(r.memoizedState,l);if(a&&(r.memoizedState=l,Ve=!0),r=r.queue,Io(Fc.bind(null,n,r,e),[e]),r.getSnapshot!==t||a||Ee!==null&&Ee.memoizedState.tag&1){if(n.flags|=2048,Rr(9,Rc.bind(null,n,r,l,t),void 0,null),Ie===null)throw Error(I(349));dn&30||Dc(n,t,l)}return l}function Dc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=me.updateQueue,t===null?(t={lastEffect:null,stores:null},me.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Rc(e,t,n,r){t.value=n,t.getSnapshot=r,Oc(t)&&$c(e)}function Fc(e,t,n){return n(function(){Oc(t)&&$c(e)})}function Oc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ft(e,n)}catch{return!0}}function $c(e){var t=It(e,1);t!==null&&dt(t,e,1,-1)}function Ri(e){var t=pt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Dr,lastRenderedState:e},t.queue=e,e=e.dispatch=Dm.bind(null,me,e),[t.memoizedState,e]}function Rr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=me.updateQueue,t===null?(t={lastEffect:null,stores:null},me.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Uc(){return st().memoizedState}function wl(e,t,n,r){var l=pt();me.flags|=e,l.memoizedState=Rr(1|t,n,void 0,r===void 0?null:r)}function ss(e,t,n,r){var l=st();r=r===void 0?null:r;var a=void 0;if(Ce!==null){var o=Ce.memoizedState;if(a=o.destroy,r!==null&&So(r,o.deps)){l.memoizedState=Rr(t,n,a,r);return}}me.flags|=e,l.memoizedState=Rr(1|t,n,a,r)}function Fi(e,t){return wl(8390656,8,e,t)}function Io(e,t){return ss(2048,8,e,t)}function Hc(e,t){return ss(4,2,e,t)}function Bc(e,t){return ss(4,4,e,t)}function Vc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Wc(e,t,n){return n=n!=null?n.concat([e]):null,ss(4,4,Vc.bind(null,t,e),n)}function Po(){}function Xc(e,t){var n=st();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&So(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Qc(e,t){var n=st();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&So(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Gc(e,t,n){return dn&21?(ft(n,t)||(n=Ju(),me.lanes|=n,fn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ve=!0),e.memoizedState=n)}function Tm(e,t){var n=se;se=n!==0&&4>n?n:4,e(!0);var r=Ws.transition;Ws.transition={};try{e(!1),t()}finally{se=n,Ws.transition=r}}function Yc(){return st().memoizedState}function Lm(e,t,n){var r=Wt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Kc(e))Zc(t,n);else if(n=zc(e,t,n,r),n!==null){var l=Oe();dt(n,e,r,l),qc(n,t,r)}}function Dm(e,t,n){var r=Wt(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Kc(e))Zc(t,l);else{var a=e.alternate;if(e.lanes===0&&(a===null||a.lanes===0)&&(a=t.lastRenderedReducer,a!==null))try{var o=t.lastRenderedState,i=a(o,n);if(l.hasEagerState=!0,l.eagerState=i,ft(i,o)){var u=t.interleaved;u===null?(l.next=l,wo(t)):(l.next=u.next,u.next=l),t.interleaved=l;return}}catch{}finally{}n=zc(e,t,l,r),n!==null&&(l=Oe(),dt(n,e,r,l),qc(n,t,r))}}function Kc(e){var t=e.alternate;return e===me||t!==null&&t===me}function Zc(e,t){hr=Hl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function qc(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,so(e,n)}}var Bl={readContext:lt,useCallback:Te,useContext:Te,useEffect:Te,useImperativeHandle:Te,useInsertionEffect:Te,useLayoutEffect:Te,useMemo:Te,useReducer:Te,useRef:Te,useState:Te,useDebugValue:Te,useDeferredValue:Te,useTransition:Te,useMutableSource:Te,useSyncExternalStore:Te,useId:Te,unstable_isNewReconciler:!1},Rm={readContext:lt,useCallback:function(e,t){return pt().memoizedState=[e,t===void 0?null:t],e},useContext:lt,useEffect:Fi,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,wl(4194308,4,Vc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return wl(4194308,4,e,t)},useInsertionEffect:function(e,t){return wl(4,2,e,t)},useMemo:function(e,t){var n=pt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=pt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Lm.bind(null,me,e),[r.memoizedState,e]},useRef:function(e){var t=pt();return e={current:e},t.memoizedState=e},useState:Ri,useDebugValue:Po,useDeferredValue:function(e){return pt().memoizedState=e},useTransition:function(){var e=Ri(!1),t=e[0];return e=Tm.bind(null,e[1]),pt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=me,l=pt();if(de){if(n===void 0)throw Error(I(407));n=n()}else{if(n=t(),Ie===null)throw Error(I(349));dn&30||Dc(r,t,n)}l.memoizedState=n;var a={value:n,getSnapshot:t};return l.queue=a,Fi(Fc.bind(null,r,a,e),[e]),r.flags|=2048,Rr(9,Rc.bind(null,r,a,n,t),void 0,null),n},useId:function(){var e=pt(),t=Ie.identifierPrefix;if(de){var n=Ct,r=kt;n=(r&~(1<<32-ct(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Lr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Mm++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Fm={readContext:lt,useCallback:Xc,useContext:lt,useEffect:Io,useImperativeHandle:Wc,useInsertionEffect:Hc,useLayoutEffect:Bc,useMemo:Qc,useReducer:Xs,useRef:Uc,useState:function(){return Xs(Dr)},useDebugValue:Po,useDeferredValue:function(e){var t=st();return Gc(t,Ce.memoizedState,e)},useTransition:function(){var e=Xs(Dr)[0],t=st().memoizedState;return[e,t]},useMutableSource:Tc,useSyncExternalStore:Lc,useId:Yc,unstable_isNewReconciler:!1},Om={readContext:lt,useCallback:Xc,useContext:lt,useEffect:Io,useImperativeHandle:Wc,useInsertionEffect:Hc,useLayoutEffect:Bc,useMemo:Qc,useReducer:Qs,useRef:Uc,useState:function(){return Qs(Dr)},useDebugValue:Po,useDeferredValue:function(e){var t=st();return Ce===null?t.memoizedState=e:Gc(t,Ce.memoizedState,e)},useTransition:function(){var e=Qs(Dr)[0],t=st().memoizedState;return[e,t]},useMutableSource:Tc,useSyncExternalStore:Lc,useId:Yc,unstable_isNewReconciler:!1};function ot(e,t){if(e&&e.defaultProps){t=pe({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function _a(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:pe({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var as={isMounted:function(e){return(e=e._reactInternals)?gn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Oe(),l=Wt(e),a=St(r,l);a.payload=t,n!=null&&(a.callback=n),t=Bt(e,a,l),t!==null&&(dt(t,e,l,r),yl(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Oe(),l=Wt(e),a=St(r,l);a.tag=1,a.payload=t,n!=null&&(a.callback=n),t=Bt(e,a,l),t!==null&&(dt(t,e,l,r),yl(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Oe(),r=Wt(e),l=St(n,r);l.tag=2,t!=null&&(l.callback=t),t=Bt(e,l,r),t!==null&&(dt(t,e,r,n),yl(t,e,r))}};function Oi(e,t,n,r,l,a,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,a,o):t.prototype&&t.prototype.isPureReactComponent?!Pr(n,r)||!Pr(l,a):!0}function Jc(e,t,n){var r=!1,l=Gt,a=t.contextType;return typeof a=="object"&&a!==null?a=lt(a):(l=Xe(t)?un:Re.current,r=t.contextTypes,a=(r=r!=null)?On(e,l):Gt),t=new t(n,a),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=as,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=a),t}function $i(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&as.enqueueReplaceState(t,t.state,null)}function za(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},jo(e);var a=t.contextType;typeof a=="object"&&a!==null?l.context=lt(a):(a=Xe(t)?un:Re.current,l.context=On(e,a)),l.state=e.memoizedState,a=t.getDerivedStateFromProps,typeof a=="function"&&(_a(e,t,a,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&as.enqueueReplaceState(l,l.state,null),$l(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function Bn(e,t){try{var n="",r=t;do n+=mf(r),r=r.return;while(r);var l=n}catch(a){l=`
Error generating stack: `+a.message+`
`+a.stack}return{value:e,source:t,stack:l,digest:null}}function Gs(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Aa(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var $m=typeof WeakMap=="function"?WeakMap:Map;function ed(e,t,n){n=St(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Wl||(Wl=!0,Ha=r),Aa(e,t)},n}function td(e,t,n){n=St(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){Aa(e,t)}}var a=e.stateNode;return a!==null&&typeof a.componentDidCatch=="function"&&(n.callback=function(){Aa(e,t),typeof r!="function"&&(Vt===null?Vt=new Set([this]):Vt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Ui(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new $m;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=ep.bind(null,e,t,n),t.then(e,e))}function Hi(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Bi(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=St(-1,1),t.tag=2,Bt(n,t,1))),n.lanes|=1),e)}var Um=_t.ReactCurrentOwner,Ve=!1;function Fe(e,t,n,r){t.child=e===null?_c(t,null,n,r):Un(t,e.child,n,r)}function Vi(e,t,n,r,l){n=n.render;var a=t.ref;return Dn(t,l),r=bo(e,t,n,r,a,l),n=Eo(),e!==null&&!Ve?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Pt(e,t,l)):(de&&n&&po(t),t.flags|=1,Fe(e,t,r,l),t.child)}function Wi(e,t,n,r,l){if(e===null){var a=n.type;return typeof a=="function"&&!Ro(a)&&a.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=a,nd(e,t,a,r,l)):(e=Cl(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(a=e.child,!(e.lanes&l)){var o=a.memoizedProps;if(n=n.compare,n=n!==null?n:Pr,n(o,r)&&e.ref===t.ref)return Pt(e,t,l)}return t.flags|=1,e=Xt(a,r),e.ref=t.ref,e.return=t,t.child=e}function nd(e,t,n,r,l){if(e!==null){var a=e.memoizedProps;if(Pr(a,r)&&e.ref===t.ref)if(Ve=!1,t.pendingProps=r=a,(e.lanes&l)!==0)e.flags&131072&&(Ve=!0);else return t.lanes=e.lanes,Pt(e,t,l)}return Ma(e,t,n,r,l)}function rd(e,t,n){var r=t.pendingProps,l=r.children,a=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ae(zn,Ge),Ge|=n;else{if(!(n&1073741824))return e=a!==null?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ae(zn,Ge),Ge|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=a!==null?a.baseLanes:n,ae(zn,Ge),Ge|=r}else a!==null?(r=a.baseLanes|n,t.memoizedState=null):r=n,ae(zn,Ge),Ge|=r;return Fe(e,t,l,n),t.child}function ld(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ma(e,t,n,r,l){var a=Xe(n)?un:Re.current;return a=On(t,a),Dn(t,l),n=bo(e,t,n,r,a,l),r=Eo(),e!==null&&!Ve?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Pt(e,t,l)):(de&&r&&po(t),t.flags|=1,Fe(e,t,n,l),t.child)}function Xi(e,t,n,r,l){if(Xe(n)){var a=!0;Ll(t)}else a=!1;if(Dn(t,l),t.stateNode===null)jl(e,t),Jc(t,n,r),za(t,n,r,l),r=!0;else if(e===null){var o=t.stateNode,i=t.memoizedProps;o.props=i;var u=o.context,c=n.contextType;typeof c=="object"&&c!==null?c=lt(c):(c=Xe(n)?un:Re.current,c=On(t,c));var g=n.getDerivedStateFromProps,x=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function";x||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(i!==r||u!==c)&&$i(t,o,r,c),Tt=!1;var y=t.memoizedState;o.state=y,$l(t,r,o,l),u=t.memoizedState,i!==r||y!==u||We.current||Tt?(typeof g=="function"&&(_a(t,n,g,r),u=t.memoizedState),(i=Tt||Oi(t,n,i,r,y,u,c))?(x||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=c,r=i):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Ac(e,t),i=t.memoizedProps,c=t.type===t.elementType?i:ot(t.type,i),o.props=c,x=t.pendingProps,y=o.context,u=n.contextType,typeof u=="object"&&u!==null?u=lt(u):(u=Xe(n)?un:Re.current,u=On(t,u));var j=n.getDerivedStateFromProps;(g=typeof j=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(i!==x||y!==u)&&$i(t,o,r,u),Tt=!1,y=t.memoizedState,o.state=y,$l(t,r,o,l);var w=t.memoizedState;i!==x||y!==w||We.current||Tt?(typeof j=="function"&&(_a(t,n,j,r),w=t.memoizedState),(c=Tt||Oi(t,n,c,r,y,w,u)||!1)?(g||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,w,u),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,w,u)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||i===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),o.props=r,o.state=w,o.context=u,r=c):(typeof o.componentDidUpdate!="function"||i===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),r=!1)}return Ta(e,t,n,r,a,l)}function Ta(e,t,n,r,l,a){ld(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return l&&zi(t,n,!1),Pt(e,t,a);r=t.stateNode,Um.current=t;var i=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Un(t,e.child,null,a),t.child=Un(t,null,i,a)):Fe(e,t,i,a),t.memoizedState=r.state,l&&zi(t,n,!0),t.child}function sd(e){var t=e.stateNode;t.pendingContext?_i(e,t.pendingContext,t.pendingContext!==t.context):t.context&&_i(e,t.context,!1),No(e,t.containerInfo)}function Qi(e,t,n,r,l){return $n(),go(l),t.flags|=256,Fe(e,t,n,r),t.child}var La={dehydrated:null,treeContext:null,retryLane:0};function Da(e){return{baseLanes:e,cachePool:null,transitions:null}}function ad(e,t,n){var r=t.pendingProps,l=fe.current,a=!1,o=(t.flags&128)!==0,i;if((i=o)||(i=e!==null&&e.memoizedState===null?!1:(l&2)!==0),i?(a=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),ae(fe,l&1),e===null)return Ia(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,a?(r=t.mode,a=t.child,o={mode:"hidden",children:o},!(r&1)&&a!==null?(a.childLanes=0,a.pendingProps=o):a=us(o,r,0,null),e=on(e,r,n,null),a.return=t,e.return=t,a.sibling=e,t.child=a,t.child.memoizedState=Da(n),t.memoizedState=La,e):_o(t,o));if(l=e.memoizedState,l!==null&&(i=l.dehydrated,i!==null))return Hm(e,t,o,r,i,l,n);if(a){a=r.fallback,o=t.mode,l=e.child,i=l.sibling;var u={mode:"hidden",children:r.children};return!(o&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=Xt(l,u),r.subtreeFlags=l.subtreeFlags&14680064),i!==null?a=Xt(i,a):(a=on(a,o,n,null),a.flags|=2),a.return=t,r.return=t,r.sibling=a,t.child=r,r=a,a=t.child,o=e.child.memoizedState,o=o===null?Da(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},a.memoizedState=o,a.childLanes=e.childLanes&~n,t.memoizedState=La,r}return a=e.child,e=a.sibling,r=Xt(a,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function _o(e,t){return t=us({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function il(e,t,n,r){return r!==null&&go(r),Un(t,e.child,null,n),e=_o(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Hm(e,t,n,r,l,a,o){if(n)return t.flags&256?(t.flags&=-257,r=Gs(Error(I(422))),il(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(a=r.fallback,l=t.mode,r=us({mode:"visible",children:r.children},l,0,null),a=on(a,l,o,null),a.flags|=2,r.return=t,a.return=t,r.sibling=a,t.child=r,t.mode&1&&Un(t,e.child,null,o),t.child.memoizedState=Da(o),t.memoizedState=La,a);if(!(t.mode&1))return il(e,t,o,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var i=r.dgst;return r=i,a=Error(I(419)),r=Gs(a,r,void 0),il(e,t,o,r)}if(i=(o&e.childLanes)!==0,Ve||i){if(r=Ie,r!==null){switch(o&-o){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|o)?0:l,l!==0&&l!==a.retryLane&&(a.retryLane=l,It(e,l),dt(r,e,l,-1))}return Do(),r=Gs(Error(I(421))),il(e,t,o,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=tp.bind(null,e),l._reactRetry=t,null):(e=a.treeContext,Ye=Ht(l.nextSibling),Ke=t,de=!0,ut=null,e!==null&&(et[tt++]=kt,et[tt++]=Ct,et[tt++]=cn,kt=e.id,Ct=e.overflow,cn=t),t=_o(t,r.children),t.flags|=4096,t)}function Gi(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Pa(e.return,t,n)}function Ys(e,t,n,r,l){var a=e.memoizedState;a===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=l)}function od(e,t,n){var r=t.pendingProps,l=r.revealOrder,a=r.tail;if(Fe(e,t,r.children,n),r=fe.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Gi(e,n,t);else if(e.tag===19)Gi(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ae(fe,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&Ul(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),Ys(t,!1,l,n,a);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&Ul(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}Ys(t,!0,n,null,a);break;case"together":Ys(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function jl(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Pt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),fn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(I(153));if(t.child!==null){for(e=t.child,n=Xt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Xt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Bm(e,t,n){switch(t.tag){case 3:sd(t),$n();break;case 5:Mc(t);break;case 1:Xe(t.type)&&Ll(t);break;case 4:No(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;ae(Fl,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ae(fe,fe.current&1),t.flags|=128,null):n&t.child.childLanes?ad(e,t,n):(ae(fe,fe.current&1),e=Pt(e,t,n),e!==null?e.sibling:null);ae(fe,fe.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return od(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),ae(fe,fe.current),r)break;return null;case 22:case 23:return t.lanes=0,rd(e,t,n)}return Pt(e,t,n)}var id,Ra,ud,cd;id=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ra=function(){};ud=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,sn(xt.current);var a=null;switch(n){case"input":l=sa(e,l),r=sa(e,r),a=[];break;case"select":l=pe({},l,{value:void 0}),r=pe({},r,{value:void 0}),a=[];break;case"textarea":l=ia(e,l),r=ia(e,r),a=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ml)}ca(n,r);var o;n=null;for(c in l)if(!r.hasOwnProperty(c)&&l.hasOwnProperty(c)&&l[c]!=null)if(c==="style"){var i=l[c];for(o in i)i.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Nr.hasOwnProperty(c)?a||(a=[]):(a=a||[]).push(c,null));for(c in r){var u=r[c];if(i=l!=null?l[c]:void 0,r.hasOwnProperty(c)&&u!==i&&(u!=null||i!=null))if(c==="style")if(i){for(o in i)!i.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&i[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(a||(a=[]),a.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,i=i?i.__html:void 0,u!=null&&i!==u&&(a=a||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(a=a||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Nr.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&ue("scroll",e),a||i===u||(a=[])):(a=a||[]).push(c,u))}n&&(a=a||[]).push("style",n);var c=a;(t.updateQueue=c)&&(t.flags|=4)}};cd=function(e,t,n,r){n!==r&&(t.flags|=4)};function lr(e,t){if(!de)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Le(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Vm(e,t,n){var r=t.pendingProps;switch(ho(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Le(t),null;case 1:return Xe(t.type)&&Tl(),Le(t),null;case 3:return r=t.stateNode,Hn(),ce(We),ce(Re),Co(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(al(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,ut!==null&&(Wa(ut),ut=null))),Ra(e,t),Le(t),null;case 5:ko(t);var l=sn(Tr.current);if(n=t.type,e!==null&&t.stateNode!=null)ud(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(I(166));return Le(t),null}if(e=sn(xt.current),al(t)){r=t.stateNode,n=t.type;var a=t.memoizedProps;switch(r[ht]=t,r[Ar]=a,e=(t.mode&1)!==0,n){case"dialog":ue("cancel",r),ue("close",r);break;case"iframe":case"object":case"embed":ue("load",r);break;case"video":case"audio":for(l=0;l<ur.length;l++)ue(ur[l],r);break;case"source":ue("error",r);break;case"img":case"image":case"link":ue("error",r),ue("load",r);break;case"details":ue("toggle",r);break;case"input":ri(r,a),ue("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!a.multiple},ue("invalid",r);break;case"textarea":si(r,a),ue("invalid",r)}ca(n,a),l=null;for(var o in a)if(a.hasOwnProperty(o)){var i=a[o];o==="children"?typeof i=="string"?r.textContent!==i&&(a.suppressHydrationWarning!==!0&&sl(r.textContent,i,e),l=["children",i]):typeof i=="number"&&r.textContent!==""+i&&(a.suppressHydrationWarning!==!0&&sl(r.textContent,i,e),l=["children",""+i]):Nr.hasOwnProperty(o)&&i!=null&&o==="onScroll"&&ue("scroll",r)}switch(n){case"input":Zr(r),li(r,a,!0);break;case"textarea":Zr(r),ai(r);break;case"select":case"option":break;default:typeof a.onClick=="function"&&(r.onclick=Ml)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Fu(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[ht]=t,e[Ar]=r,id(e,t,!1,!1),t.stateNode=e;e:{switch(o=da(n,r),n){case"dialog":ue("cancel",e),ue("close",e),l=r;break;case"iframe":case"object":case"embed":ue("load",e),l=r;break;case"video":case"audio":for(l=0;l<ur.length;l++)ue(ur[l],e);l=r;break;case"source":ue("error",e),l=r;break;case"img":case"image":case"link":ue("error",e),ue("load",e),l=r;break;case"details":ue("toggle",e),l=r;break;case"input":ri(e,r),l=sa(e,r),ue("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=pe({},r,{value:void 0}),ue("invalid",e);break;case"textarea":si(e,r),l=ia(e,r),ue("invalid",e);break;default:l=r}ca(n,l),i=l;for(a in i)if(i.hasOwnProperty(a)){var u=i[a];a==="style"?Uu(e,u):a==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Ou(e,u)):a==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&kr(e,u):typeof u=="number"&&kr(e,""+u):a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&a!=="autoFocus"&&(Nr.hasOwnProperty(a)?u!=null&&a==="onScroll"&&ue("scroll",e):u!=null&&Ja(e,a,u,o))}switch(n){case"input":Zr(e),li(e,r,!1);break;case"textarea":Zr(e),ai(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Qt(r.value));break;case"select":e.multiple=!!r.multiple,a=r.value,a!=null?An(e,!!r.multiple,a,!1):r.defaultValue!=null&&An(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=Ml)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Le(t),null;case 6:if(e&&t.stateNode!=null)cd(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(I(166));if(n=sn(Tr.current),sn(xt.current),al(t)){if(r=t.stateNode,n=t.memoizedProps,r[ht]=t,(a=r.nodeValue!==n)&&(e=Ke,e!==null))switch(e.tag){case 3:sl(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&sl(r.nodeValue,n,(e.mode&1)!==0)}a&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[ht]=t,t.stateNode=r}return Le(t),null;case 13:if(ce(fe),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(de&&Ye!==null&&t.mode&1&&!(t.flags&128))Ic(),$n(),t.flags|=98560,a=!1;else if(a=al(t),r!==null&&r.dehydrated!==null){if(e===null){if(!a)throw Error(I(318));if(a=t.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(I(317));a[ht]=t}else $n(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Le(t),a=!1}else ut!==null&&(Wa(ut),ut=null),a=!0;if(!a)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||fe.current&1?Se===0&&(Se=3):Do())),t.updateQueue!==null&&(t.flags|=4),Le(t),null);case 4:return Hn(),Ra(e,t),e===null&&_r(t.stateNode.containerInfo),Le(t),null;case 10:return vo(t.type._context),Le(t),null;case 17:return Xe(t.type)&&Tl(),Le(t),null;case 19:if(ce(fe),a=t.memoizedState,a===null)return Le(t),null;if(r=(t.flags&128)!==0,o=a.rendering,o===null)if(r)lr(a,!1);else{if(Se!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Ul(e),o!==null){for(t.flags|=128,lr(a,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)a=n,e=r,a.flags&=14680066,o=a.alternate,o===null?(a.childLanes=0,a.lanes=e,a.child=null,a.subtreeFlags=0,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null,a.stateNode=null):(a.childLanes=o.childLanes,a.lanes=o.lanes,a.child=o.child,a.subtreeFlags=0,a.deletions=null,a.memoizedProps=o.memoizedProps,a.memoizedState=o.memoizedState,a.updateQueue=o.updateQueue,a.type=o.type,e=o.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ae(fe,fe.current&1|2),t.child}e=e.sibling}a.tail!==null&&ge()>Vn&&(t.flags|=128,r=!0,lr(a,!1),t.lanes=4194304)}else{if(!r)if(e=Ul(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),lr(a,!0),a.tail===null&&a.tailMode==="hidden"&&!o.alternate&&!de)return Le(t),null}else 2*ge()-a.renderingStartTime>Vn&&n!==1073741824&&(t.flags|=128,r=!0,lr(a,!1),t.lanes=4194304);a.isBackwards?(o.sibling=t.child,t.child=o):(n=a.last,n!==null?n.sibling=o:t.child=o,a.last=o)}return a.tail!==null?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=ge(),t.sibling=null,n=fe.current,ae(fe,r?n&1|2:n&1),t):(Le(t),null);case 22:case 23:return Lo(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ge&1073741824&&(Le(t),t.subtreeFlags&6&&(t.flags|=8192)):Le(t),null;case 24:return null;case 25:return null}throw Error(I(156,t.tag))}function Wm(e,t){switch(ho(t),t.tag){case 1:return Xe(t.type)&&Tl(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Hn(),ce(We),ce(Re),Co(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ko(t),null;case 13:if(ce(fe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(I(340));$n()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ce(fe),null;case 4:return Hn(),null;case 10:return vo(t.type._context),null;case 22:case 23:return Lo(),null;case 24:return null;default:return null}}var ul=!1,De=!1,Xm=typeof WeakSet=="function"?WeakSet:Set,F=null;function _n(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){he(e,t,r)}else n.current=null}function Fa(e,t,n){try{n()}catch(r){he(e,t,r)}}var Yi=!1;function Qm(e,t){if(ja=_l,e=hc(),mo(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,a=r.focusNode;r=r.focusOffset;try{n.nodeType,a.nodeType}catch{n=null;break e}var o=0,i=-1,u=-1,c=0,g=0,x=e,y=null;t:for(;;){for(var j;x!==n||l!==0&&x.nodeType!==3||(i=o+l),x!==a||r!==0&&x.nodeType!==3||(u=o+r),x.nodeType===3&&(o+=x.nodeValue.length),(j=x.firstChild)!==null;)y=x,x=j;for(;;){if(x===e)break t;if(y===n&&++c===l&&(i=o),y===a&&++g===r&&(u=o),(j=x.nextSibling)!==null)break;x=y,y=x.parentNode}x=j}n=i===-1||u===-1?null:{start:i,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Na={focusedElem:e,selectionRange:n},_l=!1,F=t;F!==null;)if(t=F,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,F=e;else for(;F!==null;){t=F;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var C=w.memoizedProps,$=w.memoizedState,f=t.stateNode,d=f.getSnapshotBeforeUpdate(t.elementType===t.type?C:ot(t.type,C),$);f.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(I(163))}}catch(p){he(t,t.return,p)}if(e=t.sibling,e!==null){e.return=t.return,F=e;break}F=t.return}return w=Yi,Yi=!1,w}function gr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var a=l.destroy;l.destroy=void 0,a!==void 0&&Fa(t,n,a)}l=l.next}while(l!==r)}}function os(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Oa(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function dd(e){var t=e.alternate;t!==null&&(e.alternate=null,dd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[ht],delete t[Ar],delete t[Sa],delete t[Pm],delete t[_m])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function fd(e){return e.tag===5||e.tag===3||e.tag===4}function Ki(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||fd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function $a(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ml));else if(r!==4&&(e=e.child,e!==null))for($a(e,t,n),e=e.sibling;e!==null;)$a(e,t,n),e=e.sibling}function Ua(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Ua(e,t,n),e=e.sibling;e!==null;)Ua(e,t,n),e=e.sibling}var ze=null,it=!1;function At(e,t,n){for(n=n.child;n!==null;)md(e,t,n),n=n.sibling}function md(e,t,n){if(gt&&typeof gt.onCommitFiberUnmount=="function")try{gt.onCommitFiberUnmount(Jl,n)}catch{}switch(n.tag){case 5:De||_n(n,t);case 6:var r=ze,l=it;ze=null,At(e,t,n),ze=r,it=l,ze!==null&&(it?(e=ze,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ze.removeChild(n.stateNode));break;case 18:ze!==null&&(it?(e=ze,n=n.stateNode,e.nodeType===8?Hs(e.parentNode,n):e.nodeType===1&&Hs(e,n),Er(e)):Hs(ze,n.stateNode));break;case 4:r=ze,l=it,ze=n.stateNode.containerInfo,it=!0,At(e,t,n),ze=r,it=l;break;case 0:case 11:case 14:case 15:if(!De&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var a=l,o=a.destroy;a=a.tag,o!==void 0&&(a&2||a&4)&&Fa(n,t,o),l=l.next}while(l!==r)}At(e,t,n);break;case 1:if(!De&&(_n(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){he(n,t,i)}At(e,t,n);break;case 21:At(e,t,n);break;case 22:n.mode&1?(De=(r=De)||n.memoizedState!==null,At(e,t,n),De=r):At(e,t,n);break;default:At(e,t,n)}}function Zi(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Xm),t.forEach(function(r){var l=np.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function at(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var a=e,o=t,i=o;e:for(;i!==null;){switch(i.tag){case 5:ze=i.stateNode,it=!1;break e;case 3:ze=i.stateNode.containerInfo,it=!0;break e;case 4:ze=i.stateNode.containerInfo,it=!0;break e}i=i.return}if(ze===null)throw Error(I(160));md(a,o,l),ze=null,it=!1;var u=l.alternate;u!==null&&(u.return=null),l.return=null}catch(c){he(l,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)pd(t,e),t=t.sibling}function pd(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(at(t,e),mt(e),r&4){try{gr(3,e,e.return),os(3,e)}catch(C){he(e,e.return,C)}try{gr(5,e,e.return)}catch(C){he(e,e.return,C)}}break;case 1:at(t,e),mt(e),r&512&&n!==null&&_n(n,n.return);break;case 5:if(at(t,e),mt(e),r&512&&n!==null&&_n(n,n.return),e.flags&32){var l=e.stateNode;try{kr(l,"")}catch(C){he(e,e.return,C)}}if(r&4&&(l=e.stateNode,l!=null)){var a=e.memoizedProps,o=n!==null?n.memoizedProps:a,i=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{i==="input"&&a.type==="radio"&&a.name!=null&&Du(l,a),da(i,o);var c=da(i,a);for(o=0;o<u.length;o+=2){var g=u[o],x=u[o+1];g==="style"?Uu(l,x):g==="dangerouslySetInnerHTML"?Ou(l,x):g==="children"?kr(l,x):Ja(l,g,x,c)}switch(i){case"input":aa(l,a);break;case"textarea":Ru(l,a);break;case"select":var y=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!a.multiple;var j=a.value;j!=null?An(l,!!a.multiple,j,!1):y!==!!a.multiple&&(a.defaultValue!=null?An(l,!!a.multiple,a.defaultValue,!0):An(l,!!a.multiple,a.multiple?[]:"",!1))}l[Ar]=a}catch(C){he(e,e.return,C)}}break;case 6:if(at(t,e),mt(e),r&4){if(e.stateNode===null)throw Error(I(162));l=e.stateNode,a=e.memoizedProps;try{l.nodeValue=a}catch(C){he(e,e.return,C)}}break;case 3:if(at(t,e),mt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Er(t.containerInfo)}catch(C){he(e,e.return,C)}break;case 4:at(t,e),mt(e);break;case 13:at(t,e),mt(e),l=e.child,l.flags&8192&&(a=l.memoizedState!==null,l.stateNode.isHidden=a,!a||l.alternate!==null&&l.alternate.memoizedState!==null||(Mo=ge())),r&4&&Zi(e);break;case 22:if(g=n!==null&&n.memoizedState!==null,e.mode&1?(De=(c=De)||g,at(t,e),De=c):at(t,e),mt(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!g&&e.mode&1)for(F=e,g=e.child;g!==null;){for(x=F=g;F!==null;){switch(y=F,j=y.child,y.tag){case 0:case 11:case 14:case 15:gr(4,y,y.return);break;case 1:_n(y,y.return);var w=y.stateNode;if(typeof w.componentWillUnmount=="function"){r=y,n=y.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(C){he(r,n,C)}}break;case 5:_n(y,y.return);break;case 22:if(y.memoizedState!==null){Ji(x);continue}}j!==null?(j.return=y,F=j):Ji(x)}g=g.sibling}e:for(g=null,x=e;;){if(x.tag===5){if(g===null){g=x;try{l=x.stateNode,c?(a=l.style,typeof a.setProperty=="function"?a.setProperty("display","none","important"):a.display="none"):(i=x.stateNode,u=x.memoizedProps.style,o=u!=null&&u.hasOwnProperty("display")?u.display:null,i.style.display=$u("display",o))}catch(C){he(e,e.return,C)}}}else if(x.tag===6){if(g===null)try{x.stateNode.nodeValue=c?"":x.memoizedProps}catch(C){he(e,e.return,C)}}else if((x.tag!==22&&x.tag!==23||x.memoizedState===null||x===e)&&x.child!==null){x.child.return=x,x=x.child;continue}if(x===e)break e;for(;x.sibling===null;){if(x.return===null||x.return===e)break e;g===x&&(g=null),x=x.return}g===x&&(g=null),x.sibling.return=x.return,x=x.sibling}}break;case 19:at(t,e),mt(e),r&4&&Zi(e);break;case 21:break;default:at(t,e),mt(e)}}function mt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(fd(n)){var r=n;break e}n=n.return}throw Error(I(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(kr(l,""),r.flags&=-33);var a=Ki(e);Ua(e,a,l);break;case 3:case 4:var o=r.stateNode.containerInfo,i=Ki(e);$a(e,i,o);break;default:throw Error(I(161))}}catch(u){he(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Gm(e,t,n){F=e,hd(e)}function hd(e,t,n){for(var r=(e.mode&1)!==0;F!==null;){var l=F,a=l.child;if(l.tag===22&&r){var o=l.memoizedState!==null||ul;if(!o){var i=l.alternate,u=i!==null&&i.memoizedState!==null||De;i=ul;var c=De;if(ul=o,(De=u)&&!c)for(F=l;F!==null;)o=F,u=o.child,o.tag===22&&o.memoizedState!==null?eu(l):u!==null?(u.return=o,F=u):eu(l);for(;a!==null;)F=a,hd(a),a=a.sibling;F=l,ul=i,De=c}qi(e)}else l.subtreeFlags&8772&&a!==null?(a.return=l,F=a):qi(e)}}function qi(e){for(;F!==null;){var t=F;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:De||os(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!De)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:ot(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;a!==null&&Di(t,a,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Di(t,o,n)}break;case 5:var i=t.stateNode;if(n===null&&t.flags&4){n=i;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var g=c.memoizedState;if(g!==null){var x=g.dehydrated;x!==null&&Er(x)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(I(163))}De||t.flags&512&&Oa(t)}catch(y){he(t,t.return,y)}}if(t===e){F=null;break}if(n=t.sibling,n!==null){n.return=t.return,F=n;break}F=t.return}}function Ji(e){for(;F!==null;){var t=F;if(t===e){F=null;break}var n=t.sibling;if(n!==null){n.return=t.return,F=n;break}F=t.return}}function eu(e){for(;F!==null;){var t=F;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{os(4,t)}catch(u){he(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(u){he(t,l,u)}}var a=t.return;try{Oa(t)}catch(u){he(t,a,u)}break;case 5:var o=t.return;try{Oa(t)}catch(u){he(t,o,u)}}}catch(u){he(t,t.return,u)}if(t===e){F=null;break}var i=t.sibling;if(i!==null){i.return=t.return,F=i;break}F=t.return}}var Ym=Math.ceil,Vl=_t.ReactCurrentDispatcher,zo=_t.ReactCurrentOwner,rt=_t.ReactCurrentBatchConfig,re=0,Ie=null,ye=null,Ae=0,Ge=0,zn=Kt(0),Se=0,Fr=null,fn=0,is=0,Ao=0,xr=null,Be=null,Mo=0,Vn=1/0,jt=null,Wl=!1,Ha=null,Vt=null,cl=!1,Ft=null,Xl=0,yr=0,Ba=null,Nl=-1,kl=0;function Oe(){return re&6?ge():Nl!==-1?Nl:Nl=ge()}function Wt(e){return e.mode&1?re&2&&Ae!==0?Ae&-Ae:Am.transition!==null?(kl===0&&(kl=Ju()),kl):(e=se,e!==0||(e=window.event,e=e===void 0?16:ac(e.type)),e):1}function dt(e,t,n,r){if(50<yr)throw yr=0,Ba=null,Error(I(185));$r(e,n,r),(!(re&2)||e!==Ie)&&(e===Ie&&(!(re&2)&&(is|=n),Se===4&&Dt(e,Ae)),Qe(e,r),n===1&&re===0&&!(t.mode&1)&&(Vn=ge()+500,ls&&Zt()))}function Qe(e,t){var n=e.callbackNode;Af(e,t);var r=Pl(e,e===Ie?Ae:0);if(r===0)n!==null&&ui(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ui(n),t===1)e.tag===0?zm(tu.bind(null,e)):Sc(tu.bind(null,e)),Em(function(){!(re&6)&&Zt()}),n=null;else{switch(ec(r)){case 1:n=lo;break;case 4:n=Zu;break;case 16:n=Il;break;case 536870912:n=qu;break;default:n=Il}n=kd(n,gd.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function gd(e,t){if(Nl=-1,kl=0,re&6)throw Error(I(327));var n=e.callbackNode;if(Rn()&&e.callbackNode!==n)return null;var r=Pl(e,e===Ie?Ae:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ql(e,r);else{t=r;var l=re;re|=2;var a=yd();(Ie!==e||Ae!==t)&&(jt=null,Vn=ge()+500,an(e,t));do try{qm();break}catch(i){xd(e,i)}while(!0);yo(),Vl.current=a,re=l,ye!==null?t=0:(Ie=null,Ae=0,t=Se)}if(t!==0){if(t===2&&(l=ga(e),l!==0&&(r=l,t=Va(e,l))),t===1)throw n=Fr,an(e,0),Dt(e,r),Qe(e,ge()),n;if(t===6)Dt(e,r);else{if(l=e.current.alternate,!(r&30)&&!Km(l)&&(t=Ql(e,r),t===2&&(a=ga(e),a!==0&&(r=a,t=Va(e,a))),t===1))throw n=Fr,an(e,0),Dt(e,r),Qe(e,ge()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(I(345));case 2:nn(e,Be,jt);break;case 3:if(Dt(e,r),(r&130023424)===r&&(t=Mo+500-ge(),10<t)){if(Pl(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){Oe(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Ca(nn.bind(null,e,Be,jt),t);break}nn(e,Be,jt);break;case 4:if(Dt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var o=31-ct(r);a=1<<o,o=t[o],o>l&&(l=o),r&=~a}if(r=l,r=ge()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ym(r/1960))-r,10<r){e.timeoutHandle=Ca(nn.bind(null,e,Be,jt),r);break}nn(e,Be,jt);break;case 5:nn(e,Be,jt);break;default:throw Error(I(329))}}}return Qe(e,ge()),e.callbackNode===n?gd.bind(null,e):null}function Va(e,t){var n=xr;return e.current.memoizedState.isDehydrated&&(an(e,t).flags|=256),e=Ql(e,t),e!==2&&(t=Be,Be=n,t!==null&&Wa(t)),e}function Wa(e){Be===null?Be=e:Be.push.apply(Be,e)}function Km(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],a=l.getSnapshot;l=l.value;try{if(!ft(a(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Dt(e,t){for(t&=~Ao,t&=~is,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ct(t),r=1<<n;e[n]=-1,t&=~r}}function tu(e){if(re&6)throw Error(I(327));Rn();var t=Pl(e,0);if(!(t&1))return Qe(e,ge()),null;var n=Ql(e,t);if(e.tag!==0&&n===2){var r=ga(e);r!==0&&(t=r,n=Va(e,r))}if(n===1)throw n=Fr,an(e,0),Dt(e,t),Qe(e,ge()),n;if(n===6)throw Error(I(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,nn(e,Be,jt),Qe(e,ge()),null}function To(e,t){var n=re;re|=1;try{return e(t)}finally{re=n,re===0&&(Vn=ge()+500,ls&&Zt())}}function mn(e){Ft!==null&&Ft.tag===0&&!(re&6)&&Rn();var t=re;re|=1;var n=rt.transition,r=se;try{if(rt.transition=null,se=1,e)return e()}finally{se=r,rt.transition=n,re=t,!(re&6)&&Zt()}}function Lo(){Ge=zn.current,ce(zn)}function an(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,bm(n)),ye!==null)for(n=ye.return;n!==null;){var r=n;switch(ho(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Tl();break;case 3:Hn(),ce(We),ce(Re),Co();break;case 5:ko(r);break;case 4:Hn();break;case 13:ce(fe);break;case 19:ce(fe);break;case 10:vo(r.type._context);break;case 22:case 23:Lo()}n=n.return}if(Ie=e,ye=e=Xt(e.current,null),Ae=Ge=t,Se=0,Fr=null,Ao=is=fn=0,Be=xr=null,ln!==null){for(t=0;t<ln.length;t++)if(n=ln[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,a=n.pending;if(a!==null){var o=a.next;a.next=l,r.next=o}n.pending=r}ln=null}return e}function xd(e,t){do{var n=ye;try{if(yo(),vl.current=Bl,Hl){for(var r=me.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}Hl=!1}if(dn=0,Ee=Ce=me=null,hr=!1,Lr=0,zo.current=null,n===null||n.return===null){Se=1,Fr=t,ye=null;break}e:{var a=e,o=n.return,i=n,u=t;if(t=Ae,i.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,g=i,x=g.tag;if(!(g.mode&1)&&(x===0||x===11||x===15)){var y=g.alternate;y?(g.updateQueue=y.updateQueue,g.memoizedState=y.memoizedState,g.lanes=y.lanes):(g.updateQueue=null,g.memoizedState=null)}var j=Hi(o);if(j!==null){j.flags&=-257,Bi(j,o,i,a,t),j.mode&1&&Ui(a,c,t),t=j,u=c;var w=t.updateQueue;if(w===null){var C=new Set;C.add(u),t.updateQueue=C}else w.add(u);break e}else{if(!(t&1)){Ui(a,c,t),Do();break e}u=Error(I(426))}}else if(de&&i.mode&1){var $=Hi(o);if($!==null){!($.flags&65536)&&($.flags|=256),Bi($,o,i,a,t),go(Bn(u,i));break e}}a=u=Bn(u,i),Se!==4&&(Se=2),xr===null?xr=[a]:xr.push(a),a=o;do{switch(a.tag){case 3:a.flags|=65536,t&=-t,a.lanes|=t;var f=ed(a,u,t);Li(a,f);break e;case 1:i=u;var d=a.type,m=a.stateNode;if(!(a.flags&128)&&(typeof d.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(Vt===null||!Vt.has(m)))){a.flags|=65536,t&=-t,a.lanes|=t;var p=td(a,i,t);Li(a,p);break e}}a=a.return}while(a!==null)}wd(n)}catch(S){t=S,ye===n&&n!==null&&(ye=n=n.return);continue}break}while(!0)}function yd(){var e=Vl.current;return Vl.current=Bl,e===null?Bl:e}function Do(){(Se===0||Se===3||Se===2)&&(Se=4),Ie===null||!(fn&268435455)&&!(is&268435455)||Dt(Ie,Ae)}function Ql(e,t){var n=re;re|=2;var r=yd();(Ie!==e||Ae!==t)&&(jt=null,an(e,t));do try{Zm();break}catch(l){xd(e,l)}while(!0);if(yo(),re=n,Vl.current=r,ye!==null)throw Error(I(261));return Ie=null,Ae=0,Se}function Zm(){for(;ye!==null;)vd(ye)}function qm(){for(;ye!==null&&!kf();)vd(ye)}function vd(e){var t=Nd(e.alternate,e,Ge);e.memoizedProps=e.pendingProps,t===null?wd(e):ye=t,zo.current=null}function wd(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Wm(n,t),n!==null){n.flags&=32767,ye=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Se=6,ye=null;return}}else if(n=Vm(n,t,Ge),n!==null){ye=n;return}if(t=t.sibling,t!==null){ye=t;return}ye=t=e}while(t!==null);Se===0&&(Se=5)}function nn(e,t,n){var r=se,l=rt.transition;try{rt.transition=null,se=1,Jm(e,t,n,r)}finally{rt.transition=l,se=r}return null}function Jm(e,t,n,r){do Rn();while(Ft!==null);if(re&6)throw Error(I(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(I(177));e.callbackNode=null,e.callbackPriority=0;var a=n.lanes|n.childLanes;if(Mf(e,a),e===Ie&&(ye=Ie=null,Ae=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||cl||(cl=!0,kd(Il,function(){return Rn(),null})),a=(n.flags&15990)!==0,n.subtreeFlags&15990||a){a=rt.transition,rt.transition=null;var o=se;se=1;var i=re;re|=4,zo.current=null,Qm(e,n),pd(n,e),vm(Na),_l=!!ja,Na=ja=null,e.current=n,Gm(n),Cf(),re=i,se=o,rt.transition=a}else e.current=n;if(cl&&(cl=!1,Ft=e,Xl=l),a=e.pendingLanes,a===0&&(Vt=null),Ef(n.stateNode),Qe(e,ge()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(Wl)throw Wl=!1,e=Ha,Ha=null,e;return Xl&1&&e.tag!==0&&Rn(),a=e.pendingLanes,a&1?e===Ba?yr++:(yr=0,Ba=e):yr=0,Zt(),null}function Rn(){if(Ft!==null){var e=ec(Xl),t=rt.transition,n=se;try{if(rt.transition=null,se=16>e?16:e,Ft===null)var r=!1;else{if(e=Ft,Ft=null,Xl=0,re&6)throw Error(I(331));var l=re;for(re|=4,F=e.current;F!==null;){var a=F,o=a.child;if(F.flags&16){var i=a.deletions;if(i!==null){for(var u=0;u<i.length;u++){var c=i[u];for(F=c;F!==null;){var g=F;switch(g.tag){case 0:case 11:case 15:gr(8,g,a)}var x=g.child;if(x!==null)x.return=g,F=x;else for(;F!==null;){g=F;var y=g.sibling,j=g.return;if(dd(g),g===c){F=null;break}if(y!==null){y.return=j,F=y;break}F=j}}}var w=a.alternate;if(w!==null){var C=w.child;if(C!==null){w.child=null;do{var $=C.sibling;C.sibling=null,C=$}while(C!==null)}}F=a}}if(a.subtreeFlags&2064&&o!==null)o.return=a,F=o;else e:for(;F!==null;){if(a=F,a.flags&2048)switch(a.tag){case 0:case 11:case 15:gr(9,a,a.return)}var f=a.sibling;if(f!==null){f.return=a.return,F=f;break e}F=a.return}}var d=e.current;for(F=d;F!==null;){o=F;var m=o.child;if(o.subtreeFlags&2064&&m!==null)m.return=o,F=m;else e:for(o=d;F!==null;){if(i=F,i.flags&2048)try{switch(i.tag){case 0:case 11:case 15:os(9,i)}}catch(S){he(i,i.return,S)}if(i===o){F=null;break e}var p=i.sibling;if(p!==null){p.return=i.return,F=p;break e}F=i.return}}if(re=l,Zt(),gt&&typeof gt.onPostCommitFiberRoot=="function")try{gt.onPostCommitFiberRoot(Jl,e)}catch{}r=!0}return r}finally{se=n,rt.transition=t}}return!1}function nu(e,t,n){t=Bn(n,t),t=ed(e,t,1),e=Bt(e,t,1),t=Oe(),e!==null&&($r(e,1,t),Qe(e,t))}function he(e,t,n){if(e.tag===3)nu(e,e,n);else for(;t!==null;){if(t.tag===3){nu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Vt===null||!Vt.has(r))){e=Bn(n,e),e=td(t,e,1),t=Bt(t,e,1),e=Oe(),t!==null&&($r(t,1,e),Qe(t,e));break}}t=t.return}}function ep(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Oe(),e.pingedLanes|=e.suspendedLanes&n,Ie===e&&(Ae&n)===n&&(Se===4||Se===3&&(Ae&130023424)===Ae&&500>ge()-Mo?an(e,0):Ao|=n),Qe(e,t)}function jd(e,t){t===0&&(e.mode&1?(t=el,el<<=1,!(el&130023424)&&(el=4194304)):t=1);var n=Oe();e=It(e,t),e!==null&&($r(e,t,n),Qe(e,n))}function tp(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),jd(e,n)}function np(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(I(314))}r!==null&&r.delete(t),jd(e,n)}var Nd;Nd=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||We.current)Ve=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ve=!1,Bm(e,t,n);Ve=!!(e.flags&131072)}else Ve=!1,de&&t.flags&1048576&&bc(t,Rl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;jl(e,t),e=t.pendingProps;var l=On(t,Re.current);Dn(t,n),l=bo(null,t,r,e,l,n);var a=Eo();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Xe(r)?(a=!0,Ll(t)):a=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,jo(t),l.updater=as,t.stateNode=l,l._reactInternals=t,za(t,r,e,n),t=Ta(null,t,r,!0,a,n)):(t.tag=0,de&&a&&po(t),Fe(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(jl(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=lp(r),e=ot(r,e),l){case 0:t=Ma(null,t,r,e,n);break e;case 1:t=Xi(null,t,r,e,n);break e;case 11:t=Vi(null,t,r,e,n);break e;case 14:t=Wi(null,t,r,ot(r.type,e),n);break e}throw Error(I(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ot(r,l),Ma(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ot(r,l),Xi(e,t,r,l,n);case 3:e:{if(sd(t),e===null)throw Error(I(387));r=t.pendingProps,a=t.memoizedState,l=a.element,Ac(e,t),$l(t,r,null,n);var o=t.memoizedState;if(r=o.element,a.isDehydrated)if(a={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=a,t.memoizedState=a,t.flags&256){l=Bn(Error(I(423)),t),t=Qi(e,t,r,n,l);break e}else if(r!==l){l=Bn(Error(I(424)),t),t=Qi(e,t,r,n,l);break e}else for(Ye=Ht(t.stateNode.containerInfo.firstChild),Ke=t,de=!0,ut=null,n=_c(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if($n(),r===l){t=Pt(e,t,n);break e}Fe(e,t,r,n)}t=t.child}return t;case 5:return Mc(t),e===null&&Ia(t),r=t.type,l=t.pendingProps,a=e!==null?e.memoizedProps:null,o=l.children,ka(r,l)?o=null:a!==null&&ka(r,a)&&(t.flags|=32),ld(e,t),Fe(e,t,o,n),t.child;case 6:return e===null&&Ia(t),null;case 13:return ad(e,t,n);case 4:return No(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Un(t,null,r,n):Fe(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ot(r,l),Vi(e,t,r,l,n);case 7:return Fe(e,t,t.pendingProps,n),t.child;case 8:return Fe(e,t,t.pendingProps.children,n),t.child;case 12:return Fe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,a=t.memoizedProps,o=l.value,ae(Fl,r._currentValue),r._currentValue=o,a!==null)if(ft(a.value,o)){if(a.children===l.children&&!We.current){t=Pt(e,t,n);break e}}else for(a=t.child,a!==null&&(a.return=t);a!==null;){var i=a.dependencies;if(i!==null){o=a.child;for(var u=i.firstContext;u!==null;){if(u.context===r){if(a.tag===1){u=St(-1,n&-n),u.tag=2;var c=a.updateQueue;if(c!==null){c=c.shared;var g=c.pending;g===null?u.next=u:(u.next=g.next,g.next=u),c.pending=u}}a.lanes|=n,u=a.alternate,u!==null&&(u.lanes|=n),Pa(a.return,n,t),i.lanes|=n;break}u=u.next}}else if(a.tag===10)o=a.type===t.type?null:a.child;else if(a.tag===18){if(o=a.return,o===null)throw Error(I(341));o.lanes|=n,i=o.alternate,i!==null&&(i.lanes|=n),Pa(o,n,t),o=a.sibling}else o=a.child;if(o!==null)o.return=a;else for(o=a;o!==null;){if(o===t){o=null;break}if(a=o.sibling,a!==null){a.return=o.return,o=a;break}o=o.return}a=o}Fe(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,Dn(t,n),l=lt(l),r=r(l),t.flags|=1,Fe(e,t,r,n),t.child;case 14:return r=t.type,l=ot(r,t.pendingProps),l=ot(r.type,l),Wi(e,t,r,l,n);case 15:return nd(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ot(r,l),jl(e,t),t.tag=1,Xe(r)?(e=!0,Ll(t)):e=!1,Dn(t,n),Jc(t,r,l),za(t,r,l,n),Ta(null,t,r,!0,e,n);case 19:return od(e,t,n);case 22:return rd(e,t,n)}throw Error(I(156,t.tag))};function kd(e,t){return Ku(e,t)}function rp(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function nt(e,t,n,r){return new rp(e,t,n,r)}function Ro(e){return e=e.prototype,!(!e||!e.isReactComponent)}function lp(e){if(typeof e=="function")return Ro(e)?1:0;if(e!=null){if(e=e.$$typeof,e===to)return 11;if(e===no)return 14}return 2}function Xt(e,t){var n=e.alternate;return n===null?(n=nt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Cl(e,t,n,r,l,a){var o=2;if(r=e,typeof e=="function")Ro(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case jn:return on(n.children,l,a,t);case eo:o=8,l|=8;break;case ta:return e=nt(12,n,t,l|2),e.elementType=ta,e.lanes=a,e;case na:return e=nt(13,n,t,l),e.elementType=na,e.lanes=a,e;case ra:return e=nt(19,n,t,l),e.elementType=ra,e.lanes=a,e;case Mu:return us(n,l,a,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case zu:o=10;break e;case Au:o=9;break e;case to:o=11;break e;case no:o=14;break e;case Mt:o=16,r=null;break e}throw Error(I(130,e==null?e:typeof e,""))}return t=nt(o,n,t,l),t.elementType=e,t.type=r,t.lanes=a,t}function on(e,t,n,r){return e=nt(7,e,r,t),e.lanes=n,e}function us(e,t,n,r){return e=nt(22,e,r,t),e.elementType=Mu,e.lanes=n,e.stateNode={isHidden:!1},e}function Ks(e,t,n){return e=nt(6,e,null,t),e.lanes=n,e}function Zs(e,t,n){return t=nt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function sp(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=zs(0),this.expirationTimes=zs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=zs(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function Fo(e,t,n,r,l,a,o,i,u){return e=new sp(e,t,n,i,u),t===1?(t=1,a===!0&&(t|=8)):t=0,a=nt(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},jo(a),e}function ap(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:wn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Cd(e){if(!e)return Gt;e=e._reactInternals;e:{if(gn(e)!==e||e.tag!==1)throw Error(I(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Xe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(I(171))}if(e.tag===1){var n=e.type;if(Xe(n))return Cc(e,n,t)}return t}function Sd(e,t,n,r,l,a,o,i,u){return e=Fo(n,r,!0,e,l,a,o,i,u),e.context=Cd(null),n=e.current,r=Oe(),l=Wt(n),a=St(r,l),a.callback=t??null,Bt(n,a,l),e.current.lanes=l,$r(e,l,r),Qe(e,r),e}function cs(e,t,n,r){var l=t.current,a=Oe(),o=Wt(l);return n=Cd(n),t.context===null?t.context=n:t.pendingContext=n,t=St(a,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Bt(l,t,o),e!==null&&(dt(e,l,o,a),yl(e,l,o)),o}function Gl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function ru(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Oo(e,t){ru(e,t),(e=e.alternate)&&ru(e,t)}function op(){return null}var bd=typeof reportError=="function"?reportError:function(e){console.error(e)};function $o(e){this._internalRoot=e}ds.prototype.render=$o.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(I(409));cs(e,t,null,null)};ds.prototype.unmount=$o.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;mn(function(){cs(null,e,null,null)}),t[Et]=null}};function ds(e){this._internalRoot=e}ds.prototype.unstable_scheduleHydration=function(e){if(e){var t=rc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Lt.length&&t!==0&&t<Lt[n].priority;n++);Lt.splice(n,0,e),n===0&&sc(e)}};function Uo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function fs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function lu(){}function ip(e,t,n,r,l){if(l){if(typeof r=="function"){var a=r;r=function(){var c=Gl(o);a.call(c)}}var o=Sd(t,r,e,0,null,!1,!1,"",lu);return e._reactRootContainer=o,e[Et]=o.current,_r(e.nodeType===8?e.parentNode:e),mn(),o}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var i=r;r=function(){var c=Gl(u);i.call(c)}}var u=Fo(e,0,!1,null,null,!1,!1,"",lu);return e._reactRootContainer=u,e[Et]=u.current,_r(e.nodeType===8?e.parentNode:e),mn(function(){cs(t,u,n,r)}),u}function ms(e,t,n,r,l){var a=n._reactRootContainer;if(a){var o=a;if(typeof l=="function"){var i=l;l=function(){var u=Gl(o);i.call(u)}}cs(t,o,e,l)}else o=ip(n,t,e,l,r);return Gl(o)}tc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ir(t.pendingLanes);n!==0&&(so(t,n|1),Qe(t,ge()),!(re&6)&&(Vn=ge()+500,Zt()))}break;case 13:mn(function(){var r=It(e,1);if(r!==null){var l=Oe();dt(r,e,1,l)}}),Oo(e,1)}};ao=function(e){if(e.tag===13){var t=It(e,134217728);if(t!==null){var n=Oe();dt(t,e,134217728,n)}Oo(e,134217728)}};nc=function(e){if(e.tag===13){var t=Wt(e),n=It(e,t);if(n!==null){var r=Oe();dt(n,e,t,r)}Oo(e,t)}};rc=function(){return se};lc=function(e,t){var n=se;try{return se=e,t()}finally{se=n}};ma=function(e,t,n){switch(t){case"input":if(aa(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=rs(r);if(!l)throw Error(I(90));Lu(r),aa(r,l)}}}break;case"textarea":Ru(e,n);break;case"select":t=n.value,t!=null&&An(e,!!n.multiple,t,!1)}};Vu=To;Wu=mn;var up={usingClientEntryPoint:!1,Events:[Hr,Sn,rs,Hu,Bu,To]},sr={findFiberByHostInstance:rn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},cp={bundleType:sr.bundleType,version:sr.version,rendererPackageName:sr.rendererPackageName,rendererConfig:sr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:_t.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Gu(e),e===null?null:e.stateNode},findFiberByHostInstance:sr.findFiberByHostInstance||op,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var dl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!dl.isDisabled&&dl.supportsFiber)try{Jl=dl.inject(cp),gt=dl}catch{}}qe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=up;qe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Uo(t))throw Error(I(200));return ap(e,t,null,n)};qe.createRoot=function(e,t){if(!Uo(e))throw Error(I(299));var n=!1,r="",l=bd;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=Fo(e,1,!1,null,null,n,!1,r,l),e[Et]=t.current,_r(e.nodeType===8?e.parentNode:e),new $o(t)};qe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(I(188)):(e=Object.keys(e).join(","),Error(I(268,e)));return e=Gu(t),e=e===null?null:e.stateNode,e};qe.flushSync=function(e){return mn(e)};qe.hydrate=function(e,t,n){if(!fs(t))throw Error(I(200));return ms(null,e,t,!0,n)};qe.hydrateRoot=function(e,t,n){if(!Uo(e))throw Error(I(405));var r=n!=null&&n.hydratedSources||null,l=!1,a="",o=bd;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(a=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=Sd(t,null,e,1,n??null,l,!1,a,o),e[Et]=t.current,_r(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new ds(t)};qe.render=function(e,t,n){if(!fs(t))throw Error(I(200));return ms(null,e,t,!1,n)};qe.unmountComponentAtNode=function(e){if(!fs(e))throw Error(I(40));return e._reactRootContainer?(mn(function(){ms(null,null,e,!1,function(){e._reactRootContainer=null,e[Et]=null})}),!0):!1};qe.unstable_batchedUpdates=To;qe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!fs(n))throw Error(I(200));if(e==null||e._reactInternals===void 0)throw Error(I(38));return ms(e,t,n,!1,r)};qe.version="18.3.1-next-f1338f8080-20240426";function Ed(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Ed)}catch(e){console.error(e)}}Ed(),Eu.exports=qe;var dp=Eu.exports,su=dp;Js.createRoot=su.createRoot,Js.hydrateRoot=su.hydrateRoot;/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var fp={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mp=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),X=(e,t)=>{const n=N.forwardRef(({color:r="currentColor",size:l=24,strokeWidth:a=2,absoluteStrokeWidth:o,className:i="",children:u,...c},g)=>N.createElement("svg",{ref:g,...fp,width:l,height:l,stroke:r,strokeWidth:o?Number(a)*24/Number(l):a,className:["lucide",`lucide-${mp(e)}`,i].join(" "),...c},[...t.map(([x,y])=>N.createElement(x,y)),...Array.isArray(u)?u:[u]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ho=X("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pp=X("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hp=X("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gp=X("ArrowUpNarrowWide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const au=X("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ou=X("Brush",[["path",{d:"m9.06 11.9 8.07-8.06a2.85 2.85 0 1 1 4.03 4.03l-8.06 8.08",key:"1styjt"}],["path",{d:"M7.07 14.94c-1.66 0-3 1.35-3 3.02 0 1.33-2.5 1.52-2 2.02 1.08 1.1 2.49 2.02 4 2.02 2.2 0 4-1.8 4-4.04a3.01 3.01 0 0 0-3-3.02z",key:"z0l1mu"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xp=X("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yp=X("CheckSquare",[["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}],["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11",key:"1jnkn4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vp=X("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wp=X("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jp=X("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yl=X("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Np=X("Crop",[["path",{d:"M6 2v14a2 2 0 0 0 2 2h14",key:"ron5a4"}],["path",{d:"M18 22V8a2 2 0 0 0-2-2H2",key:"7s9ehn"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ps=X("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kp=X("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cp=X("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Id=X("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sp=X("FolderDown",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}],["path",{d:"M12 10v6",key:"1bos4e"}],["path",{d:"m15 13-3 3-3-3",key:"6j2sf0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bo=X("FolderPlus",[["path",{d:"M12 10v6",key:"1bos4e"}],["path",{d:"M9 13h6",key:"1uhe8q"}],["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vr=X("Folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bp=X("GitCompare",[["circle",{cx:"18",cy:"18",r:"3",key:"1xkwt0"}],["circle",{cx:"6",cy:"6",r:"3",key:"1lh9wr"}],["path",{d:"M13 6h3a2 2 0 0 1 2 2v7",key:"1yeb86"}],["path",{d:"M11 18H8a2 2 0 0 1-2-2V9",key:"19pyzm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ep=X("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wr=X("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ip=X("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pp=X("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iu=X("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _p=X("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pd=X("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vr=X("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z",key:"ymcmye"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jr=X("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zp=X("Redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xa=X("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ap=X("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mp=X("RotateCw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kl=X("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uu=X("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _d=X("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tp=X("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cu=X("Shuffle",[["path",{d:"M2 18h1.4c1.3 0 2.5-.6 3.3-1.7l6.1-8.6c.7-1.1 2-1.7 3.3-1.7H22",key:"1wmou1"}],["path",{d:"m18 2 4 4-4 4",key:"pucp1d"}],["path",{d:"M2 6h1.9c1.5 0 2.9.9 3.6 2.2",key:"10bdb2"}],["path",{d:"M22 18h-5.9c-1.3 0-2.6-.7-3.3-1.8l-.5-.8",key:"vgxac0"}],["path",{d:"m18 14 4 4-4 4",key:"10pe0f"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lp=X("Sliders",[["line",{x1:"4",x2:"4",y1:"21",y2:"14",key:"1p332r"}],["line",{x1:"4",x2:"4",y1:"10",y2:"3",key:"gb41h5"}],["line",{x1:"12",x2:"12",y1:"21",y2:"12",key:"hf2csr"}],["line",{x1:"12",x2:"12",y1:"8",y2:"3",key:"1kfi7u"}],["line",{x1:"20",x2:"20",y1:"21",y2:"16",key:"1lhrwl"}],["line",{x1:"20",x2:"20",y1:"12",y2:"3",key:"16vvfq"}],["line",{x1:"2",x2:"6",y1:"14",y2:"14",key:"1uebub"}],["line",{x1:"10",x2:"14",y1:"8",y2:"8",key:"1yglbp"}],["line",{x1:"18",x2:"22",y1:"16",y2:"16",key:"1jxqpz"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pn=X("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zd=X("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gn=X("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const du=X("Type",[["polyline",{points:"4 7 4 4 20 4 20 7",key:"1nosan"}],["line",{x1:"9",x2:"15",y1:"20",y2:"20",key:"swin9y"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dp=X("Undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fu=X("Wand2",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72Z",key:"1bcowg"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rp=X("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yt=X("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fp=X("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Op=X("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $p=X("ZoomOut",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),mu=async function(e){e.headers.set("Origin","https://labs.google"),e.headers.set("Referer","https://labs.google");try{const t=await fetch(e.url,{body:e.body,method:e.method,headers:e.headers}),n=await t.text();return t.ok?{Ok:n}:{Err:new Error(n)}}catch(t){return{Err:t instanceof Error?t:new Error("Failed to fetch.")}}};var Zl,Ad;class pu{constructor(t){Yo(this,Zl);Gr(this,"credentials");if(!t.authorizationKey&&!t.cookie)throw new Error("Authorization token and Cookie both are missing.");if(t.cookie&&t.cookie.length<70)throw new Error("Invalid cookie was provided.");this.credentials=structuredClone(t),this.credentials.cookie&&!this.credentials.cookie.startsWith("__Secure-next-auth.session-token=")&&(this.credentials.cookie="__Secure-next-auth.session-token="+this.credentials.cookie)}async getAuthToken(t=!1){if(!this.credentials.cookie)return{Err:new Error("Cookie is required for generating auth token.")};const n={url:"https://labs.google/fx/api/auth/session",method:"GET",headers:new Headers({Cookie:this.credentials.cookie})},r=await mu(n);if(r.Err||!r.Ok)return{Err:r.Err};try{const l=JSON.parse(r.Ok);return l.access_token?(t&&(this.credentials.authorizationKey=l.access_token),{Ok:l.access_token}):{Err:Error("Access token is missing from response: "+r.Ok)}}catch{return{Err:Error("Failed to parse response: "+r.Ok)}}}async generateImage(t){const n=await Ko(this,Zl,Ad).call(this);if(n.Err)return{Err:n.Err};t.model=="IMAGEN_4"&&(t.model="IMAGEN_3_5");const r={method:"POST",body:JSON.stringify({userInput:{candidatesCount:t.count||4,prompts:[t.prompt],seed:t.seed||0},aspectRatio:t.aspectRatio||"IMAGE_ASPECT_RATIO_LANDSCAPE",modelInput:{modelNameType:t.model||"IMAGEN_3_5"},clientContext:{sessionId:";1740658431200",tool:"IMAGE_FX"}}),url:"https://aisandbox-pa.googleapis.com/v1:runImageFx",headers:new Headers({Authorization:"Bearer "+this.credentials.authorizationKey})},l=await mu(r);if(l.Err||!l.Ok)return{Err:l.Err};try{const o=JSON.parse(l.Ok).imagePanels[0].generatedImages;return Array.isArray(o)?{Ok:o}:{Err:Error("Invalid response recieved: "+l.Ok)}}catch{return{Err:Error("Failed to parse JSON: "+l.Ok)}}}}Zl=new WeakSet,Ad=async function(){return!this.credentials.authorizationKey&&!this.credentials.cookie?{Err:Error("Authorization token and Cookie both are missing.")}:(this.credentials.cookie&&!this.credentials.authorizationKey&&await this.getAuthToken(!0),{Ok:!0})};const Up=["IMAGEN_2","IMAGEN_3","IMAGEN_4","IMAGEN_3_1","IMAGEN_3_5","IMAGEN_3_PORTRAIT","IMAGEN_3_LANDSCAPE","IMAGEN_3_PORTRAIT_THREE_FOUR","IMAGEN_3_LANDSCAPE_FOUR_THREE"];class Hp{constructor(t){Gr(this,"fx",null);t&&(this.fx=new pu({authorizationKey:t}))}updateAuth(t){this.fx=new pu({authorizationKey:t})}async generateImages(t){if(!this.fx)throw new Error("ImageFX not initialized. Please provide authentication token.");try{const n=await this.fx.generateImage({prompt:t.prompt,count:t.count||4,model:t.model||"IMAGEN_4",aspectRatio:t.aspectRatio||"IMAGE_ASPECT_RATIO_LANDSCAPE",...t.seed!==void 0&&{seed:t.seed}});if(n.Err)throw new Error(n.Err.message||"ImageFX generation failed");if(!n.Ok||!Array.isArray(n.Ok))throw new Error("No images generated or invalid response format");return n.Ok.map((r,l)=>({id:`imagefx-${Date.now()}-${l}`,url:`data:image/png;base64,${r.encodedImage}`,prompt:t.prompt,timestamp:Date.now(),service:"imagefx"}))}catch(n){throw console.error("ImageFX generation error:",n),n instanceof Error?n:new Error("Unknown error occurred during ImageFX generation")}}isConfigured(){return this.fx!==null}}const Bp=e=>{switch(e){case"square":return"IMAGE_ASPECT_RATIO_SQUARE";case"portrait":return"IMAGE_ASPECT_RATIO_PORTRAIT";case"portrait_3_4":return"IMAGE_ASPECT_RATIO_PORTRAIT_THREE_FOUR";case"landscape":return"IMAGE_ASPECT_RATIO_LANDSCAPE";case"landscape_4_3":return"IMAGE_ASPECT_RATIO_LANDSCAPE_FOUR_THREE";default:return"IMAGE_ASPECT_RATIO_LANDSCAPE"}},Vp=[{value:"square",label:"Square (1:1)"},{value:"portrait",label:"Portrait (3:4)"},{value:"portrait_3_4",label:"Portrait (3:4) - ImageFX"},{value:"landscape",label:"Landscape (4:3)"},{value:"landscape_4_3",label:"Landscape (4:3) - ImageFX"}],Wp=[{value:"512x512",label:"512 × 512"},{value:"768x768",label:"768 × 768"},{value:"1024x1024",label:"1024 × 1024"},{value:"1024x768",label:"1024 × 768"},{value:"768x1024",label:"768 × 1024"},{value:"1536x1024",label:"1536 × 1024"},{value:"1024x1536",label:"1024 × 1536"}],Xp=[{value:"flux",label:"Flux",description:"High-quality, balanced model"},{value:"turbo",label:"Turbo",description:"Fast generation, good quality"},{value:"flux-realism",label:"Flux Realism",description:"Photorealistic images"},{value:"flux-cablyai",label:"Flux CablyAI",description:"Artistic and creative"},{value:"flux-anime",label:"Flux Anime",description:"Anime and manga style"},{value:"flux-3d",label:"Flux 3D",description:"3D rendered style"},{value:"any-dark",label:"Any Dark",description:"Dark themed images"}],Qp=[{value:"pollinations",label:"Pollinations AI",description:"Free, fast, no auth required"},{value:"imagefx",label:"ImageFX",description:"Google's ImageFX (requires auth)"}],Gp=({settings:e,onSettingsChange:t,onGenerate:n,isGenerating:r})=>{const l=e.prompt||"",a=c=>{c.preventDefault(),l.trim()&&!r&&n(l.trim())},o=c=>{u("prompt",c)},i=c=>{c.key==="Enter"&&(c.ctrlKey||c.metaKey)&&(c.preventDefault(),a(c))},u=(c,g)=>{t({...e,[c]:g})};return s.jsxs("div",{className:"w-80 bg-sidebar border-r border-matte flex flex-col relative",children:[s.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-purple-950/10 via-transparent to-transparent pointer-events-none"}),s.jsx("div",{className:"relative p-6 border-b border-matte/50",children:s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-accent-primary to-accent-muted shadow-glow-sm",children:s.jsx(pn,{className:"w-5 h-5 text-white"})}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-xl font-bold text-white tracking-tight",children:"Imagen AI"}),s.jsx("p",{className:"text-xs text-gray-400 font-medium",children:"AI Image Generator"})]})]})}),s.jsx("div",{className:"relative p-6 border-b border-matte/50",children:s.jsxs("form",{onSubmit:a,className:"space-y-5",children:[s.jsxs("div",{children:[s.jsxs("label",{className:"text-sm font-semibold text-gray-200 mb-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full"}),"Describe your image"]}),s.jsx("span",{className:"text-xs text-gray-500 font-normal",children:"Ctrl+Enter to generate"})]}),s.jsxs("div",{className:"relative",children:[s.jsx("textarea",{value:l,onChange:c=>o(c.target.value),onKeyDown:i,placeholder:"A beautiful landscape with mountains and a lake at sunset...",className:"input-primary w-full h-28 resize-none text-sm leading-relaxed",disabled:r}),s.jsx("div",{className:"absolute inset-0 rounded-xl pointer-events-none bg-gradient-to-r from-transparent via-transparent to-accent-primary/5"})]}),s.jsxs("div",{className:"flex justify-between items-center mt-2",children:[s.jsx("div",{className:"text-xs text-gray-500",children:l.length>0&&`${l.length} characters`}),s.jsx("div",{className:"text-xs text-gray-500",children:l.length>500&&"Consider shortening for better results"})]})]}),s.jsx("button",{type:"submit",disabled:!l.trim()||r,className:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-3 text-sm font-semibold",children:r?s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Generating..."]}):s.jsxs(s.Fragment,{children:[s.jsx(pn,{className:"w-4 h-4"}),"Generate Images"]})})]})}),s.jsxs("div",{className:"relative p-6 flex-1 overflow-y-auto custom-scrollbar",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx("div",{className:"p-1.5 rounded-lg bg-gradient-to-br from-accent-primary/20 to-accent-muted/20 border border-accent-primary/30",children:s.jsx(_d,{className:"w-4 h-4 text-accent-primary"})}),s.jsx("h2",{className:"text-lg font-bold text-white tracking-tight",children:"Settings"})]}),s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{children:[s.jsxs("label",{className:"text-sm font-semibold text-gray-200 mb-4 flex items-center gap-2",children:[s.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full"}),"AI Service"]}),s.jsx("div",{className:"space-y-3",children:Qp.map(c=>s.jsxs("label",{className:"group flex items-start gap-4 p-4 rounded-xl border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300 hover:bg-card/50 backdrop-blur-sm",children:[s.jsxs("div",{className:"relative mt-0.5",children:[s.jsx("input",{type:"radio",name:"aiService",value:c.value,checked:e.aiService===c.value,onChange:g=>u("aiService",g.target.value),className:"sr-only"}),s.jsx("div",{className:`w-4 h-4 rounded-full border-2 transition-all duration-300 ${e.aiService===c.value?"border-accent-primary bg-accent-primary shadow-glow-sm":"border-gray-500 group-hover:border-accent-primary/70"}`,children:e.aiService===c.value&&s.jsx("div",{className:"w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"})})]}),s.jsxs("div",{className:"flex-1",children:[s.jsx("div",{className:"text-sm font-semibold text-white group-hover:text-accent-secondary transition-colors",children:c.label}),s.jsx("div",{className:"text-xs text-gray-400 mt-1 leading-relaxed",children:c.description})]})]},c.value))})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"text-sm font-semibold text-gray-200 mb-3 flex items-center gap-2",children:[s.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full"}),"Aspect Ratio"]}),s.jsxs("div",{className:"relative",children:[s.jsx("select",{value:e.aspectRatio,onChange:c=>u("aspectRatio",c.target.value),className:"input-primary w-full appearance-none cursor-pointer",children:Vp.map(c=>s.jsx("option",{value:c.value,className:"bg-input text-white",children:c.label},c.value))}),s.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none",children:s.jsx("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"text-sm font-semibold text-gray-200 mb-3 flex items-center gap-2",children:[s.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full"}),"Resolution"]}),s.jsxs("div",{className:"relative",children:[s.jsx("select",{value:e.resolution,onChange:c=>u("resolution",c.target.value),className:"input-primary w-full appearance-none cursor-pointer",children:Wp.map(c=>s.jsx("option",{value:c.value,className:"bg-input text-white",children:c.label},c.value))}),s.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none",children:s.jsx("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),e.aiService==="pollinations"&&s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{children:[s.jsxs("label",{className:"text-sm font-semibold text-gray-200 mb-3 flex items-center gap-2",children:[s.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full"}),"Pollinations Model"]}),s.jsx("div",{className:"space-y-2",children:Xp.map(c=>s.jsxs("label",{className:"group flex items-start gap-4 p-3 rounded-xl border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300 hover:bg-card/50 backdrop-blur-sm",children:[s.jsxs("div",{className:"relative mt-0.5",children:[s.jsx("input",{type:"radio",name:"pollinationsModel",value:c.value,checked:e.pollinationsModel===c.value,onChange:g=>u("pollinationsModel",g.target.value),className:"sr-only"}),s.jsx("div",{className:`w-4 h-4 rounded-full border-2 transition-all duration-300 ${e.pollinationsModel===c.value?"border-accent-primary bg-accent-primary shadow-glow-sm":"border-gray-500 group-hover:border-accent-primary/70"}`,children:e.pollinationsModel===c.value&&s.jsx("div",{className:"w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"})})]}),s.jsxs("div",{className:"flex-1",children:[s.jsx("div",{className:"text-sm font-semibold text-white group-hover:text-accent-secondary transition-colors",children:c.label}),s.jsx("div",{className:"text-xs text-gray-400 mt-1 leading-relaxed",children:c.description})]})]},c.value))})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"text-sm font-semibold text-gray-200 mb-3 flex items-center gap-2",children:[s.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full"}),"Advanced Options"]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsxs("label",{className:"text-xs font-medium text-gray-300 mb-2 flex items-center gap-2",children:[s.jsx(cu,{className:"w-3 h-3"}),"Seed (optional)"]}),s.jsx("input",{type:"number",value:e.pollinationsSeed||"",onChange:c=>u("pollinationsSeed",c.target.value?parseInt(c.target.value):void 0),placeholder:"Random seed for reproducible results",className:"input-primary w-full text-sm",min:"0",max:"999999"})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[s.jsxs("label",{className:"flex items-center gap-2 p-3 rounded-lg border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300",children:[s.jsx("input",{type:"checkbox",checked:e.pollinationsNoLogo,onChange:c=>u("pollinationsNoLogo",c.target.checked),className:"sr-only"}),s.jsx("div",{className:`w-4 h-4 rounded border-2 transition-all duration-300 flex items-center justify-center ${e.pollinationsNoLogo?"border-accent-primary bg-accent-primary":"border-gray-500"}`,children:e.pollinationsNoLogo&&s.jsx("svg",{className:"w-2 h-2 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),s.jsxs("div",{children:[s.jsx("div",{className:"text-xs font-medium text-white",children:"No Logo"}),s.jsx("div",{className:"text-xs text-gray-400",children:"Remove watermark"})]})]}),s.jsxs("label",{className:"flex items-center gap-2 p-3 rounded-lg border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300",children:[s.jsx("input",{type:"checkbox",checked:e.pollinationsEnhance,onChange:c=>u("pollinationsEnhance",c.target.checked),className:"sr-only"}),s.jsx("div",{className:`w-4 h-4 rounded border-2 transition-all duration-300 flex items-center justify-center ${e.pollinationsEnhance?"border-accent-primary bg-accent-primary":"border-gray-500"}`,children:e.pollinationsEnhance&&s.jsx("svg",{className:"w-2 h-2 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),s.jsxs("div",{children:[s.jsxs("div",{className:"text-xs font-medium text-white flex items-center gap-1",children:[s.jsx(Fp,{className:"w-3 h-3"}),"Enhance"]}),s.jsx("div",{className:"text-xs text-gray-400",children:"Improve prompts"})]})]}),s.jsxs("label",{className:"flex items-center gap-2 p-3 rounded-lg border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300",children:[s.jsx("input",{type:"checkbox",checked:e.pollinationsSafe,onChange:c=>u("pollinationsSafe",c.target.checked),className:"sr-only"}),s.jsx("div",{className:`w-4 h-4 rounded border-2 transition-all duration-300 flex items-center justify-center ${e.pollinationsSafe?"border-accent-primary bg-accent-primary":"border-gray-500"}`,children:e.pollinationsSafe&&s.jsx("svg",{className:"w-2 h-2 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),s.jsxs("div",{children:[s.jsxs("div",{className:"text-xs font-medium text-white flex items-center gap-1",children:[s.jsx(Tp,{className:"w-3 h-3"}),"Safe Mode"]}),s.jsx("div",{className:"text-xs text-gray-400",children:"Content filtering"})]})]}),s.jsxs("label",{className:"flex items-center gap-2 p-3 rounded-lg border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300",children:[s.jsx("input",{type:"checkbox",checked:e.pollinationsPrivate,onChange:c=>u("pollinationsPrivate",c.target.checked),className:"sr-only"}),s.jsx("div",{className:`w-4 h-4 rounded border-2 transition-all duration-300 flex items-center justify-center ${e.pollinationsPrivate?"border-accent-primary bg-accent-primary":"border-gray-500"}`,children:e.pollinationsPrivate&&s.jsx("svg",{className:"w-2 h-2 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),s.jsxs("div",{children:[s.jsxs("div",{className:"text-xs font-medium text-white flex items-center gap-1",children:[e.pollinationsPrivate?s.jsx(kp,{className:"w-3 h-3"}):s.jsx(Cp,{className:"w-3 h-3"}),"Private"]}),s.jsx("div",{className:"text-xs text-gray-400",children:"Private generation"})]})]})]})]})]})]}),e.aiService==="imagefx"&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"ImageFX Auth Token"}),s.jsx("input",{type:"password",value:e.imagefxAuth||"",onChange:c=>u("imagefxAuth",c.target.value),placeholder:"Enter your ImageFX authentication token",className:"input-primary w-full"}),s.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Required for ImageFX service. See documentation for setup."})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"ImageFX Model"}),s.jsx("select",{value:e.imagefxModel||"IMAGEN_4",onChange:c=>u("imagefxModel",c.target.value),className:"input-primary w-full",children:Up.map(c=>s.jsx("option",{value:c,children:c.replace(/_/g," ")},c))}),s.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Choose the ImageFX model for generation."})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"text-sm font-semibold text-gray-200 mb-3 flex items-center gap-2",children:[s.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full"}),"Advanced Options"]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsxs("label",{className:"text-xs font-medium text-gray-300 mb-2 flex items-center gap-2",children:[s.jsx(cu,{className:"w-3 h-3"}),"Seed (optional)"]}),s.jsx("input",{type:"number",value:e.imagefxSeed||"",onChange:c=>u("imagefxSeed",c.target.value?parseInt(c.target.value):void 0),placeholder:"Seed for reference image",className:"input-primary w-full text-sm",min:"0"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-xs font-medium text-gray-300 mb-2",children:"Number of Images"}),s.jsxs("select",{value:e.imagefxCount,onChange:c=>u("imagefxCount",parseInt(c.target.value)),className:"input-primary w-full text-sm",children:[s.jsx("option",{value:1,children:"1 image"}),s.jsx("option",{value:2,children:"2 images"}),s.jsx("option",{value:3,children:"3 images"}),s.jsx("option",{value:4,children:"4 images"}),s.jsx("option",{value:5,children:"5 images"}),s.jsx("option",{value:6,children:"6 images"}),s.jsx("option",{value:7,children:"7 images"}),s.jsx("option",{value:8,children:"8 images"})]}),s.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"ImageFX supports generating 1-8 images per request."})]})]})]}),!e.imagefxAuth&&s.jsxs("div",{className:"flex items-start gap-2 p-3 bg-yellow-900/20 border border-yellow-600/30 rounded-lg",children:[s.jsx(Ho,{className:"w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0"}),s.jsxs("div",{className:"text-xs text-yellow-200",children:[s.jsx("p",{className:"font-medium mb-1",children:"Authentication Required"}),s.jsx("p",{children:"ImageFX requires an authentication token to generate images. Please add your token above."})]})]})]})]})]})]})},Yp=({isOpen:e,position:t,image:n,collections:r,onClose:l,onAddToCollection:a,onCreateNewCollection:o})=>{const i=N.useRef(null),[u,c]=N.useState(!1);if(N.useEffect(()=>{const $=d=>{i.current&&!i.current.contains(d.target)&&l()},f=d=>{d.key==="Escape"&&l()};return e&&(document.addEventListener("mousedown",$),document.addEventListener("keydown",f)),()=>{document.removeEventListener("mousedown",$),document.removeEventListener("keydown",f)}},[e,l]),!e||!n)return null;const g=$=>{u||(c(!0),a($,n.id),setTimeout(()=>{c(!1),l()},300))},x=()=>{u||(c(!0),o([n.id]),setTimeout(()=>{c(!1),l()},300))},y=$=>$.imageIds.includes(n.id),j=250,w=Math.min(300,r.length*40+120),C={x:t.x+j>window.innerWidth?Math.max(10,t.x-j):t.x,y:t.y+w>window.innerHeight?Math.max(10,t.y-w):t.y};return s.jsxs("div",{ref:i,className:"fixed z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-xl py-2 min-w-[200px]",style:{left:C.x,top:C.y},children:[s.jsxs("div",{className:"px-3 py-2 border-b border-gray-600",children:[s.jsx("p",{className:"text-sm font-medium text-white",children:"Add to Collection"}),s.jsx("p",{className:"text-xs text-gray-400 truncate",children:n.prompt})]}),s.jsx("div",{className:"max-h-60 overflow-y-auto",children:r.length>0?r.map($=>{const f=y($);return s.jsxs("button",{onClick:()=>!f&&!u&&g($.id),disabled:f||u,className:`w-full px-3 py-2 text-left hover:bg-gray-700 transition-colors flex items-center gap-2 ${f||u?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,children:[s.jsx("div",{className:"w-3 h-3 rounded-full flex-shrink-0",style:{backgroundColor:$.color||"#3b82f6"}}),s.jsx(vr,{className:"w-4 h-4 text-gray-400 flex-shrink-0"}),s.jsx("span",{className:"text-sm text-white truncate flex-1",children:$.name}),f&&s.jsx(vp,{className:"w-4 h-4 text-green-500 flex-shrink-0"})]},$.id)}):s.jsx("div",{className:"px-3 py-4 text-center",children:s.jsx("p",{className:"text-sm text-gray-400",children:"No collections yet"})})}),s.jsx("div",{className:"border-t border-gray-600 mt-2",children:s.jsxs("button",{onClick:x,disabled:u,className:`w-full px-3 py-2 text-left hover:bg-gray-700 transition-colors flex items-center gap-2 text-accent-primary ${u?"opacity-50 cursor-not-allowed":""}`,children:[u?s.jsx(Ip,{className:"w-4 h-4 animate-spin"}):s.jsx(Bo,{className:"w-4 h-4"}),s.jsx("span",{className:"text-sm",children:u?"Creating...":"Create New Collection"})]})})]})},qs=["#3b82f6","#ef4444","#10b981","#f59e0b","#8b5cf6","#ec4899","#06b6d4","#84cc16","#f97316","#6366f1"],Kp=({isOpen:e,onClose:t,onCreateCollection:n,imagesToAdd:r,previewImages:l=[]})=>{const[a,o]=N.useState(""),[i,u]=N.useState(""),[c,g]=N.useState(qs[0]),x=N.useRef(null);N.useEffect(()=>{e&&(o(""),u(""),g(qs[0]),setTimeout(()=>{var w;(w=x.current)==null||w.focus()},100))},[e]),N.useEffect(()=>{const w=C=>{C.key==="Escape"&&t()};return e&&(document.addEventListener("keydown",w),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",w),document.body.style.overflow="unset"}},[e,t]);const y=w=>{w.preventDefault(),a.trim()&&(n({name:a.trim(),description:i.trim()||void 0,imageIds:r,color:c}),t())},j=w=>{w.key==="Enter"&&(w.ctrlKey||w.metaKey)&&y(w)};return e?s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-gray-800 rounded-lg border border-gray-600 w-full max-w-md mx-4",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-600",children:[s.jsx("h3",{className:"text-lg font-semibold text-white",children:"Create Collection"}),s.jsx("button",{onClick:t,className:"p-1 text-gray-400 hover:text-white transition-colors",children:s.jsx(yt,{className:"w-5 h-5"})})]}),s.jsxs("form",{onSubmit:y,className:"p-4",children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Collection Name *"}),s.jsx("input",{ref:x,type:"text",value:a,onChange:w=>o(w.target.value),onKeyDown:j,placeholder:"Enter collection name...",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none",maxLength:50})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description (Optional)"}),s.jsx("textarea",{value:i,onChange:w=>u(w.target.value),onKeyDown:j,placeholder:"Enter description...",rows:2,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none resize-none",maxLength:200})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Color"}),s.jsx("div",{className:"flex items-center gap-2 flex-wrap",children:qs.map(w=>s.jsx("button",{type:"button",onClick:()=>g(w),className:`w-8 h-8 rounded-full border-2 transition-all ${c===w?"border-white scale-110":"border-gray-600 hover:border-gray-400"}`,style:{backgroundColor:w}},w))})]}),l.length>0&&s.jsxs("div",{className:"mb-4",children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Images to Add (",r.length,")"]}),s.jsxs("div",{className:"flex gap-2 overflow-x-auto pb-2",children:[l.slice(0,5).map(w=>s.jsx("div",{className:"flex-shrink-0",children:s.jsx("img",{src:w.url,alt:w.prompt,className:"w-12 h-12 object-cover rounded border border-gray-600"})},w.id)),l.length>5&&s.jsx("div",{className:"flex-shrink-0 w-12 h-12 bg-gray-700 rounded border border-gray-600 flex items-center justify-center",children:s.jsxs("span",{className:"text-xs text-gray-400",children:["+",l.length-5]})})]})]}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsxs("button",{type:"submit",disabled:!a.trim(),className:"flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-accent-primary hover:bg-accent-secondary disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors",children:[s.jsx(Kl,{className:"w-4 h-4"}),"Create Collection"]}),s.jsx("button",{type:"button",onClick:t,className:"px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors",children:"Cancel"})]}),s.jsx("p",{className:"text-xs text-gray-500 mt-2",children:"Press Ctrl+Enter to create quickly"})]})]})}):null},Zp=({image:e,onDownload:t,onDelete:n,onPreview:r,onEdit:l,isSelected:a,isSelectionMode:o,onToggleSelection:i,onRightClick:u,collections:c=[]})=>{const[g,x]=N.useState(!1),[y,j]=N.useState(0),w=p=>new Date(p).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),C=c.filter(p=>!p.isDefault&&p.imageIds.includes(e.id));if(e.isLoading)return s.jsxs("div",{className:"bg-card rounded-2xl border border-matte overflow-hidden backdrop-blur-sm animate-fade-in",children:[s.jsxs("div",{className:"aspect-square bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center relative",children:[s.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-accent-primary/10 to-accent-muted/5"}),s.jsxs("div",{className:"text-center relative z-10",children:[s.jsx("div",{className:"w-10 h-10 border-4 border-accent-primary border-t-transparent rounded-full animate-spin mx-auto mb-3 shadow-glow-sm"}),s.jsx("p",{className:"text-sm text-gray-300 font-medium",children:"Generating..."}),s.jsx("div",{className:"w-16 h-1 bg-gray-700 rounded-full mt-3 mx-auto overflow-hidden",children:s.jsx("div",{className:"h-full bg-gradient-to-r from-accent-primary to-accent-secondary rounded-full animate-pulse-glow"})})]})]}),s.jsxs("div",{className:"p-4",children:[s.jsx("p",{className:"text-sm text-gray-300 line-clamp-2 leading-relaxed",children:e.prompt}),s.jsxs("div",{className:"flex items-center gap-3 mt-3 text-xs text-gray-400",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(Yl,{className:"w-3 h-3"}),s.jsx("span",{children:w(e.timestamp)})]}),s.jsx("div",{className:"w-1 h-1 bg-gray-600 rounded-full"}),s.jsx("span",{className:"capitalize font-medium",children:e.service})]})]})]});const $=()=>{o&&i?i(e.id):r(e)},f=p=>{p.preventDefault(),u&&u(e,{x:p.clientX,y:p.clientY})},d=()=>{x(!0)},m=()=>{if(y<3){x(!1),j(S=>S+1);const p=new Image;p.onload=()=>x(!1),p.onerror=()=>x(!0),p.src=`${e.url}?retry=${y+1}`}};return s.jsxs("div",{className:`bg-card rounded-2xl border overflow-hidden group hover:border-accent-primary/50 transition-all duration-500 hover:shadow-glow-sm backdrop-blur-sm animate-fade-in cursor-pointer ${a?"border-accent-primary shadow-glow-sm ring-2 ring-accent-primary/30":"border-matte"}`,onClick:$,onContextMenu:f,children:[s.jsxs("div",{className:"relative aspect-square overflow-hidden",children:[g?s.jsx("div",{className:"w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center",children:s.jsxs("div",{className:"text-center p-4",children:[s.jsx("div",{className:"w-12 h-12 bg-red-600/20 rounded-full flex items-center justify-center mx-auto mb-3",children:s.jsx(Ho,{className:"w-6 h-6 text-red-400"})}),s.jsx("p",{className:"text-sm text-gray-300 mb-3",children:"Failed to load image"}),y<3&&s.jsxs("button",{onClick:p=>{p.stopPropagation(),m()},className:"px-3 py-1 bg-accent-primary hover:bg-accent-secondary text-white text-xs rounded-lg transition-colors",children:["Retry (",3-y," left)"]})]})}):s.jsx("img",{src:e.url,alt:e.prompt,className:"w-full h-full object-cover transition-all duration-500 group-hover:scale-110",loading:"lazy",onError:d}),s.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),s.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-10",children:s.jsx("div",{className:"bg-black/70 backdrop-blur-sm rounded-full px-3 py-2 border border-white/20",children:s.jsx("div",{className:"text-white text-xs font-medium",children:"Click to preview"})})}),s.jsx("div",{className:"absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-all duration-300 z-20",children:s.jsxs("div",{className:"flex items-center gap-2",children:[l&&!e.isLoading&&s.jsx("button",{onClick:p=>{p.stopPropagation(),l(e)},className:"w-8 h-8 bg-purple-600 hover:bg-purple-700 text-white rounded-lg flex items-center justify-center shadow-xl transition-all duration-200",title:"Edit",children:s.jsx(Vr,{className:"w-4 h-4"})}),s.jsx("button",{onClick:p=>{p.stopPropagation(),t(e)},className:"w-8 h-8 bg-accent-primary hover:bg-accent-secondary text-white rounded-lg flex items-center justify-center shadow-xl transition-all duration-200",title:"Download",children:s.jsx(ps,{className:"w-4 h-4"})}),!e.isLoading&&s.jsx("button",{onClick:p=>{p.stopPropagation(),n(e)},className:"w-8 h-8 bg-red-600 hover:bg-red-700 text-white rounded-lg flex items-center justify-center shadow-xl border border-red-500/50 transition-all duration-200",title:"Delete",children:s.jsx(Gn,{className:"w-4 h-4"})})]})}),o&&s.jsx("div",{className:"absolute top-3 left-3 z-30",children:s.jsxs("div",{className:`w-6 h-6 rounded border-2 flex items-center justify-center transition-all duration-200 ${a?"bg-accent-primary border-accent-primary text-white":"bg-black/50 border-white/30 backdrop-blur-sm"}`,children:[a&&s.jsx(yp,{className:"w-4 h-4"}),!a&&s.jsx(zd,{className:"w-4 h-4"})]})}),s.jsx("div",{className:`absolute top-3 right-3 transition-opacity duration-300 ${o?"opacity-100":"opacity-0 group-hover:opacity-100"}`,children:s.jsx("div",{className:"px-2 py-1 bg-black/50 backdrop-blur-sm rounded-lg border border-white/10",children:s.jsxs("div",{className:"flex items-center gap-1 text-xs text-white",children:[s.jsx(pn,{className:"w-3 h-3"}),s.jsx("span",{className:"capitalize font-medium",children:e.service})]})})}),C.length>0&&s.jsx("div",{className:"absolute bottom-3 left-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:s.jsxs("div",{className:"flex items-center gap-1",children:[C.slice(0,3).map(p=>s.jsx("div",{className:"w-3 h-3 rounded-full border border-white/30",style:{backgroundColor:p.color||"#3b82f6"},title:p.name},p.id)),C.length>3&&s.jsx("div",{className:"w-3 h-3 rounded-full bg-gray-600 border border-white/30 flex items-center justify-center",children:s.jsx("span",{className:"text-[8px] text-white font-bold",children:"+"})})]})})]}),s.jsxs("div",{className:"p-4",children:[s.jsx("p",{className:"text-sm text-gray-300 line-clamp-2 mb-3 leading-relaxed",children:e.prompt}),s.jsxs("div",{className:"flex items-center gap-3 text-xs text-gray-400",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(Yl,{className:"w-3 h-3"}),s.jsx("span",{children:w(e.timestamp)})]}),s.jsx("div",{className:"w-1 h-1 bg-gray-600 rounded-full"}),s.jsx("span",{className:"capitalize font-medium",children:e.service})]})]})]})},qp=({images:e,totalImages:t,onDownload:n,onDelete:r,onPreview:l,onRefresh:a,onEdit:o,selectedImages:i=new Set,isSelectionMode:u=!1,onToggleSelection:c,onToggleSelectionMode:g,onSelectAll:x,onDeselectAll:y,onBatchDownload:j,onBatchDelete:w,onBatchExport:C,searchQuery:$="",onSearchChange:f,filterService:d="all",onFilterServiceChange:m,sortBy:p="newest",onSortChange:S,dateFilter:A="all",onDateFilterChange:M,onOpenComparison:_,onOpenCollections:R,collections:T=[],onAddToCollection:te,onCreateCollection:ve})=>{const[H,oe]=N.useState({isOpen:!1,position:{x:0,y:0},image:null}),[we,xe]=N.useState({isOpen:!1,imagesToAdd:[]}),le=(Y,je)=>{oe({isOpen:!0,position:je,image:Y})},P=()=>{oe({isOpen:!1,position:{x:0,y:0},image:null})},Q=(Y,je)=>{te&&te(Y,je)},U=Y=>{xe({isOpen:!0,imagesToAdd:Y}),P()},ne=()=>{xe({isOpen:!1,imagesToAdd:[]})},q=Y=>{ve&&ve(Y)},J=(t||0)>0,V=$||d!=="all"||A!=="all";return e.length===0?s.jsxs("div",{className:"h-full w-full flex items-center justify-center bg-app relative",children:[s.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-accent-primary/5 via-transparent to-accent-muted/5"}),s.jsxs("div",{className:"text-center relative z-10 max-w-md mx-auto px-6",children:[s.jsxs("div",{className:"relative mb-6",children:[s.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-accent-primary to-accent-muted rounded-full blur-xl opacity-30 animate-pulse-glow"}),J&&V?s.jsx(uu,{className:"w-20 h-20 text-accent-primary mx-auto relative z-10"}):s.jsx(pn,{className:"w-20 h-20 text-accent-primary mx-auto relative z-10"})]}),J&&V?s.jsxs(s.Fragment,{children:[s.jsx("h2",{className:"text-2xl font-bold text-white mb-3 tracking-tight",children:"No Results Found"}),s.jsx("p",{className:"text-gray-400 leading-relaxed",children:"No images match your current search and filter criteria. Try adjusting your filters or search terms."}),s.jsxs("div",{className:"mt-8 flex flex-col items-center gap-4",children:[s.jsxs("div",{className:"flex items-center gap-2 px-4 py-2 bg-card/50 backdrop-blur-sm rounded-xl border border-matte",children:[s.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full animate-pulse"}),s.jsx("span",{className:"text-xs text-gray-400 font-medium",children:"Try different search terms..."})]}),s.jsxs("button",{onClick:()=>{f==null||f(""),m==null||m("all"),M==null||M("all")},className:"flex items-center gap-2 px-3 py-2 bg-card/30 hover:bg-card/50 text-gray-400 hover:text-white rounded-lg border border-matte hover:border-accent-primary/50 transition-all duration-200",children:[s.jsx(yt,{className:"w-4 h-4"}),s.jsx("span",{className:"text-sm",children:"Clear Filters"})]})]})]}):s.jsxs(s.Fragment,{children:[s.jsx("h2",{className:"text-2xl font-bold text-white mb-3 tracking-tight",children:"Ready to Create"}),s.jsx("p",{className:"text-gray-400 leading-relaxed",children:'Enter a prompt in the sidebar and click "Generate Images" to start creating amazing AI-generated artwork.'}),s.jsxs("div",{className:"mt-8 flex flex-col items-center gap-4",children:[s.jsxs("div",{className:"flex items-center gap-2 px-4 py-2 bg-card/50 backdrop-blur-sm rounded-xl border border-matte",children:[s.jsx("div",{className:"w-2 h-2 bg-accent-primary rounded-full animate-pulse"}),s.jsx("span",{className:"text-xs text-gray-400 font-medium",children:"Waiting for your creativity..."})]}),a&&s.jsxs("button",{onClick:a,className:"flex items-center gap-2 px-3 py-2 bg-card/30 hover:bg-card/50 text-gray-400 hover:text-white rounded-lg border border-matte hover:border-accent-primary/50 transition-all duration-200",children:[s.jsx(Xa,{className:"w-4 h-4"}),s.jsx("span",{className:"text-sm",children:"Refresh Gallery"})]})]})]})]})]}):s.jsxs("div",{className:"h-full w-full overflow-y-auto bg-app relative custom-scrollbar",children:[s.jsx("div",{className:"absolute inset-0 opacity-5",children:s.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-accent-primary/10 via-transparent to-accent-muted/10"})}),s.jsxs("div",{className:"relative z-10 p-8",children:[s.jsxs("div",{className:"mb-6 space-y-4",children:[s.jsxs("div",{className:"relative",children:[s.jsx(uu,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),s.jsx("input",{type:"text",placeholder:"Search images by prompt or service...",value:$,onChange:Y=>f==null?void 0:f(Y.target.value),className:"w-full pl-10 pr-4 py-3 bg-card border border-matte rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none transition-colors"})]}),s.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Id,{className:"w-4 h-4 text-gray-400"}),s.jsxs("select",{value:d,onChange:Y=>m==null?void 0:m(Y.target.value),className:"bg-card border border-matte rounded-lg px-3 py-2 text-white text-sm focus:border-accent-primary focus:outline-none",children:[s.jsx("option",{value:"all",children:"All Services"}),s.jsx("option",{value:"pollinations",children:"Pollinations"}),s.jsx("option",{value:"imagefx",children:"ImageFX"})]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Yl,{className:"w-4 h-4 text-gray-400"}),s.jsxs("select",{value:A,onChange:Y=>M==null?void 0:M(Y.target.value),className:"bg-card border border-matte rounded-lg px-3 py-2 text-white text-sm focus:border-accent-primary focus:outline-none",children:[s.jsx("option",{value:"all",children:"All Time"}),s.jsx("option",{value:"today",children:"Today"}),s.jsx("option",{value:"week",children:"This Week"}),s.jsx("option",{value:"month",children:"This Month"})]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(gp,{className:"w-4 h-4 text-gray-400"}),s.jsxs("select",{value:p,onChange:Y=>S==null?void 0:S(Y.target.value),className:"bg-card border border-matte rounded-lg px-3 py-2 text-white text-sm focus:border-accent-primary focus:outline-none",children:[s.jsx("option",{value:"newest",children:"Newest First"}),s.jsx("option",{value:"oldest",children:"Oldest First"}),s.jsx("option",{value:"prompt",children:"By Prompt (A-Z)"})]})]}),s.jsx("div",{className:"ml-auto text-sm text-gray-400",children:e.length===(t||e.length)?`${e.length} image${e.length!==1?"s":""}`:`${e.length} of ${t||e.length} image${(t||e.length)!==1?"s":""}`})]})]}),s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsx("div",{className:"flex items-center gap-4",children:s.jsxs("h2",{className:"text-xl font-bold text-white",children:["Generated Images",u&&i.size>0&&s.jsxs("span",{className:"text-accent-primary ml-2",children:["(",i.size," selected)"]})]})}),s.jsxs("div",{className:"flex items-center gap-2",children:[u&&s.jsxs(s.Fragment,{children:[s.jsx("button",{onClick:x,className:"flex items-center gap-2 px-3 py-2 bg-card/30 hover:bg-card/50 text-gray-400 hover:text-white rounded-lg border border-matte hover:border-accent-primary/50 transition-all duration-200 text-sm",children:"Select All"}),s.jsx("button",{onClick:y,className:"flex items-center gap-2 px-3 py-2 bg-card/30 hover:bg-card/50 text-gray-400 hover:text-white rounded-lg border border-matte hover:border-accent-primary/50 transition-all duration-200 text-sm",children:"Deselect All"}),i.size>0&&s.jsxs(s.Fragment,{children:[s.jsxs("button",{onClick:()=>U(Array.from(i)),className:"flex items-center gap-2 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all duration-200 text-sm",children:[s.jsx(Bo,{className:"w-4 h-4"}),"Add to Collection (",i.size,")"]}),s.jsxs("button",{onClick:j,className:"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 text-sm",children:[s.jsx(Sp,{className:"w-4 h-4"}),"Download (",i.size,")"]}),s.jsxs("button",{onClick:C,className:"flex items-center gap-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-all duration-200 text-sm",children:[s.jsx(hp,{className:"w-4 h-4"}),"Export (",i.size,")"]}),s.jsxs("button",{onClick:w,className:"flex items-center gap-2 px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all duration-200 text-sm",children:[s.jsx(Gn,{className:"w-4 h-4"}),"Delete (",i.size,")"]})]})]}),g&&s.jsxs("button",{onClick:g,className:`flex items-center gap-2 px-3 py-2 rounded-lg border transition-all duration-200 text-sm ${u?"bg-accent-primary hover:bg-accent-secondary text-white border-accent-primary":"bg-card/30 hover:bg-card/50 text-gray-400 hover:text-white border-matte hover:border-accent-primary/50"}`,children:[s.jsx(_p,{className:"w-4 h-4"}),u?"Exit Selection":"Select"]}),R&&s.jsxs("button",{onClick:R,className:"flex items-center gap-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-all duration-200 text-sm",children:[s.jsx(vr,{className:"w-4 h-4"}),"Collections"]}),_&&e.length>=2&&s.jsxs("button",{onClick:_,className:"flex items-center gap-2 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all duration-200 text-sm",children:[s.jsx(bp,{className:"w-4 h-4"}),"Compare"]}),a&&s.jsxs("button",{onClick:a,className:"flex items-center gap-2 px-3 py-2 bg-card/30 hover:bg-card/50 text-gray-400 hover:text-white rounded-lg border border-matte hover:border-accent-primary/50 transition-all duration-200 text-sm",children:[s.jsx(Xa,{className:"w-4 h-4"}),"Refresh"]})]})]}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.map((Y,je)=>s.jsx("div",{style:{animationDelay:`${je*100}ms`},className:"animate-slide-up",children:s.jsx(Zp,{image:Y,onDownload:n,onDelete:r,onPreview:l,onEdit:o,isSelected:i.has(Y.id),isSelectionMode:u,onToggleSelection:c,onRightClick:le,collections:T})},Y.id))})]}),s.jsx(Yp,{isOpen:H.isOpen,position:H.position,image:H.image,collections:T,onClose:P,onAddToCollection:Q,onCreateNewCollection:U}),s.jsx(Kp,{isOpen:we.isOpen,onClose:ne,onCreateCollection:q,imagesToAdd:we.imagesToAdd,previewImages:e.filter(Y=>we.imagesToAdd.includes(Y.id))})]})},Jp=({image:e,isOpen:t,onClose:n,onDownload:r,onDelete:l,onEdit:a})=>{if(N.useEffect(()=>{const u=c=>{c.key==="Escape"&&n()};return t&&(document.addEventListener("keydown",u),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",u),document.body.style.overflow="unset"}},[t,n]),!t||!e)return null;const o=u=>new Date(u).toLocaleString(),i=()=>{const u={"512x512":"512 × 512","768x768":"768 × 768","1024x1024":"1024 × 1024","1024x768":"1024 × 768","768x1024":"768 × 1024","1536x1024":"1536 × 1024","1024x1536":"1024 × 1536"};for(const[c,g]of Object.entries(u))if(e.url.includes(c.replace("x","%2C")))return g;return"Unknown"};return s.jsxs("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[s.jsx("div",{className:"absolute inset-0 bg-black/80 backdrop-blur-md",onClick:n}),s.jsxs("div",{className:"relative w-full max-w-6xl max-h-[90vh] mx-4 bg-card rounded-3xl border border-matte shadow-2xl overflow-hidden animate-fade-in",children:[s.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-matte bg-gradient-to-r from-card to-card/80",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-8 h-8 rounded-xl bg-gradient-to-br from-accent-primary to-accent-secondary flex items-center justify-center",children:s.jsx(wr,{className:"w-4 h-4 text-white"})}),s.jsxs("div",{children:[s.jsx("h2",{className:"text-xl font-bold text-white",children:"Generated Image Preview"}),s.jsx("p",{className:"text-sm text-gray-400",children:"Click outside or press Escape to close"})]})]}),s.jsxs("div",{className:"flex items-center gap-3",children:[a&&s.jsxs("button",{onClick:()=>{a(e),n()},className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-all duration-200",children:[s.jsx(Vr,{className:"w-4 h-4"}),"Edit"]}),s.jsxs("button",{onClick:()=>r(e),className:"btn-primary flex items-center gap-2 px-4 py-2",children:[s.jsx(ps,{className:"w-4 h-4"}),"Download"]}),s.jsxs("button",{onClick:()=>{l(e),n()},className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-all duration-200 border border-red-500/50",children:[s.jsx(Gn,{className:"w-4 h-4"}),"Delete"]}),s.jsx("button",{onClick:n,className:"p-2 rounded-lg hover:bg-matte transition-colors text-gray-400 hover:text-white",children:s.jsx(yt,{className:"w-5 h-5"})})]})]}),s.jsxs("div",{className:"flex flex-col lg:flex-row max-h-[calc(90vh-80px)]",children:[s.jsx("div",{className:"flex-1 p-6 flex items-center justify-center bg-gradient-to-br from-gray-900/50 to-gray-800/50",children:s.jsx("div",{className:"relative max-w-full max-h-full",children:s.jsx("img",{src:e.url,alt:e.prompt,className:"max-w-full max-h-[60vh] object-contain rounded-2xl shadow-2xl",loading:"lazy"})})}),s.jsxs("div",{className:"w-full lg:w-96 border-l border-matte bg-card/50 overflow-y-auto custom-scrollbar",children:[s.jsxs("div",{className:"p-6 border-b border-matte",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx("div",{className:"w-6 h-6 rounded-lg bg-gradient-to-br from-accent-primary to-accent-secondary flex items-center justify-center",children:s.jsx(Pd,{className:"w-3 h-3 text-white"})}),s.jsx("h3",{className:"text-lg font-bold text-white",children:"Prompt Details"})]}),s.jsx("div",{className:"space-y-3",children:s.jsxs("div",{children:[s.jsx("label",{className:"block text-xs font-medium text-gray-400 mb-2",children:"Generated Prompt"}),s.jsx("div",{className:"bg-input rounded-xl p-4 border border-matte",children:s.jsx("p",{className:"text-sm text-gray-300 leading-relaxed",children:e.prompt})})]})})]}),s.jsxs("div",{className:"p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx("div",{className:"w-6 h-6 rounded-lg bg-gradient-to-br from-accent-primary to-accent-secondary flex items-center justify-center",children:s.jsx(_d,{className:"w-3 h-3 text-white"})}),s.jsx("h3",{className:"text-lg font-bold text-white",children:"Image Details"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{className:"bg-input rounded-xl p-3 border border-matte",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx(pn,{className:"w-3 h-3 text-accent-primary"}),s.jsx("span",{className:"text-xs font-medium text-gray-400",children:"Service"})]}),s.jsx("p",{className:"text-sm font-semibold text-white capitalize",children:e.service})]}),s.jsxs("div",{className:"bg-input rounded-xl p-3 border border-matte",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx(wr,{className:"w-3 h-3 text-accent-primary"}),s.jsx("span",{className:"text-xs font-medium text-gray-400",children:"Dimensions"})]}),s.jsx("p",{className:"text-sm font-semibold text-white",children:i()})]})]}),s.jsxs("div",{className:"bg-input rounded-xl p-3 border border-matte",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx(Yl,{className:"w-3 h-3 text-accent-primary"}),s.jsx("span",{className:"text-xs font-medium text-gray-400",children:"Generated"})]}),s.jsx("p",{className:"text-sm font-semibold text-white",children:o(e.timestamp)})]}),s.jsxs("div",{className:"bg-input rounded-xl p-3 border border-matte",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx(Ep,{className:"w-3 h-3 text-accent-primary"}),s.jsx("span",{className:"text-xs font-medium text-gray-400",children:"Image ID"})]}),s.jsx("p",{className:"text-xs font-mono text-gray-300 break-all",children:e.id})]}),e.filename&&s.jsxs("div",{className:"bg-input rounded-xl p-3 border border-matte",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx(wr,{className:"w-3 h-3 text-accent-primary"}),s.jsx("span",{className:"text-xs font-medium text-gray-400",children:"Filename"})]}),s.jsx("p",{className:"text-xs font-mono text-gray-300 break-all",children:e.filename})]})]})]})]})]})]})]})},eh=({isOpen:e,onToggle:t,onPromptSelect:n})=>{const[r,l]=N.useState("library"),[a,o]=N.useState([]),[i,u]=N.useState(null),[c,g]=N.useState(""),[x,y]=N.useState(""),[j,w]=N.useState(""),[C,$]=N.useState(""),[f,d]=N.useState(!1);if(N.useEffect(()=>{(async()=>{if(window.electronAPI)try{const T=await window.electronAPI.loadPrompts();T.success&&T.prompts&&o(T.prompts)}catch(T){console.error("Failed to load prompts:",T)}})()},[]),N.useEffect(()=>{(async()=>{if(window.electronAPI&&a.length>0)try{await window.electronAPI.savePrompts(a)}catch(T){console.error("Failed to save prompts:",T)}})()},[a]),!e)return s.jsx("button",{onClick:t,className:"fixed top-4 right-4 z-50 w-10 h-10 bg-black/20 hover:bg-black/40 backdrop-blur-sm border border-white/10 rounded-lg flex items-center justify-center transition-all duration-300 hover:scale-105",title:"Open Prompt Tools",children:s.jsx(wp,{className:"w-5 h-5 text-white rotate-180"})});const m=()=>{if(!c.trim()||!x.trim())return;const R={id:Date.now().toString(),title:c.trim(),prompt:x.trim(),createdAt:Date.now(),updatedAt:Date.now()};o(T=>[R,...T]),g(""),y("")},p=R=>{u(R),g(R.title),y(R.prompt)},S=()=>{!i||!c.trim()||!x.trim()||(o(R=>R.map(T=>T.id===i.id?{...T,title:c.trim(),prompt:x.trim(),updatedAt:Date.now()}:T)),u(null),g(""),y(""))},A=R=>{o(T=>T.filter(te=>te.id!==R))},M=async()=>{if(j.trim()){d(!0),$("");try{const R=`Enhance this image generation prompt to be more detailed, creative, and effective for AI image generation. Make it more descriptive and artistic while keeping the core concept. Original prompt: "${j.trim()}". Return only the enhanced prompt without any additional text, quotes, or explanation.`,T=await fetch("https://text.pollinations.ai/",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[{role:"user",content:R}],model:"openai"})});if(T.ok){const ve=(await T.text()).trim().replace(/^["']|["']$/g,"");$(ve||"Enhancement completed but result was empty. Please try again.")}else throw new Error(`HTTP ${T.status}: ${T.statusText}`)}catch(R){console.error("Prompt enhancement failed:",R),$("Failed to enhance prompt. Please check your internet connection and try again.")}finally{d(!1)}}},_=()=>{C&&n(C)};return s.jsxs("div",{className:"fixed top-0 right-0 h-full w-96 max-w-[90vw] bg-card border-l border-matte shadow-2xl z-40 flex flex-col",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-matte bg-gradient-to-r from-card to-card/80",children:[s.jsx("h2",{className:"text-lg font-bold text-white",children:"Prompt Tools"}),s.jsx("button",{onClick:t,className:"p-2 rounded-lg hover:bg-matte transition-colors text-gray-400 hover:text-white",children:s.jsx(yt,{className:"w-5 h-5"})})]}),s.jsxs("div",{className:"flex border-b border-matte",children:[s.jsx("button",{onClick:()=>l("library"),className:`flex-1 px-4 py-3 text-sm font-medium transition-colors ${r==="library"?"text-accent-primary border-b-2 border-accent-primary bg-accent-primary/5":"text-gray-400 hover:text-white hover:bg-matte/50"}`,children:s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx(au,{className:"w-4 h-4"}),"Library"]})}),s.jsx("button",{onClick:()=>l("enhancer"),className:`flex-1 px-4 py-3 text-sm font-medium transition-colors ${r==="enhancer"?"text-accent-primary border-b-2 border-accent-primary bg-accent-primary/5":"text-gray-400 hover:text-white hover:bg-matte/50"}`,children:s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx(fu,{className:"w-4 h-4"}),"Enhancer"]})})]}),s.jsxs("div",{className:"flex-1 overflow-y-auto custom-scrollbar",children:[r==="library"&&s.jsxs("div",{className:"p-4 space-y-4",children:[s.jsxs("div",{className:"bg-input rounded-xl p-4 border border-matte",children:[s.jsxs("h3",{className:"text-sm font-semibold text-white mb-3 flex items-center gap-2",children:[s.jsx(jr,{className:"w-4 h-4"}),i?"Edit Prompt":"Add New Prompt"]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("input",{type:"text",placeholder:"Prompt title...",value:c,onChange:R=>g(R.target.value),className:"w-full px-3 py-2 bg-card border border-matte rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none"}),s.jsx("textarea",{placeholder:"Enter your prompt...",value:x,onChange:R=>y(R.target.value),rows:3,className:"w-full px-3 py-2 bg-card border border-matte rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none resize-none"}),s.jsxs("div",{className:"flex gap-2",children:[s.jsxs("button",{onClick:i?S:m,disabled:!c.trim()||!x.trim(),className:"flex-1 btn-primary flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed",children:[s.jsx(Kl,{className:"w-4 h-4"}),i?"Update":"Save"]}),i&&s.jsx("button",{onClick:()=>{u(null),g(""),y("")},className:"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors",children:"Cancel"})]})]})]}),s.jsx("div",{className:"space-y-3",children:a.length===0?s.jsxs("div",{className:"text-center py-8 text-gray-400",children:[s.jsx(au,{className:"w-12 h-12 mx-auto mb-3 opacity-50"}),s.jsx("p",{className:"text-sm",children:"No saved prompts yet"}),s.jsx("p",{className:"text-xs mt-1",children:"Add your first prompt above"})]}):a.map(R=>s.jsxs("div",{className:"bg-input rounded-xl p-4 border border-matte hover:border-accent-primary/50 transition-colors",children:[s.jsxs("div",{className:"flex items-start justify-between mb-2",children:[s.jsx("h4",{className:"font-medium text-white text-sm",children:R.title}),s.jsxs("div",{className:"flex gap-1",children:[s.jsx("button",{onClick:()=>p(R),className:"p-1 rounded hover:bg-matte text-gray-400 hover:text-white transition-colors",title:"Edit",children:s.jsx(Vr,{className:"w-3 h-3"})}),s.jsx("button",{onClick:()=>A(R.id),className:"p-1 rounded hover:bg-red-600/20 text-gray-400 hover:text-red-400 transition-colors",title:"Delete",children:s.jsx(Gn,{className:"w-3 h-3"})})]})]}),s.jsx("p",{className:"text-xs text-gray-300 mb-3 line-clamp-3",children:R.prompt}),s.jsx("button",{onClick:()=>n(R.prompt),className:"w-full px-3 py-2 bg-accent-primary/20 hover:bg-accent-primary/30 text-accent-primary rounded-lg text-xs font-medium transition-colors",children:"Use This Prompt"})]},R.id))})]}),r==="enhancer"&&s.jsxs("div",{className:"p-4 space-y-4",children:[s.jsxs("div",{className:"bg-input rounded-xl p-4 border border-matte",children:[s.jsxs("h3",{className:"text-sm font-semibold text-white mb-3 flex items-center gap-2",children:[s.jsx(pn,{className:"w-4 h-4"}),"AI Prompt Enhancer"]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("textarea",{placeholder:"Enter your basic prompt here...",value:j,onChange:R=>w(R.target.value),rows:4,className:"w-full px-3 py-2 bg-card border border-matte rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none resize-none"}),s.jsx("button",{onClick:M,disabled:!j.trim()||f,className:"w-full btn-primary flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed",children:f?s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),"Enhancing..."]}):s.jsxs(s.Fragment,{children:[s.jsx(fu,{className:"w-4 h-4"}),"Enhance Prompt"]})})]})]}),C&&s.jsxs("div",{className:"bg-input rounded-xl p-4 border border-matte",children:[s.jsxs("h4",{className:"text-sm font-semibold text-white mb-3 flex items-center gap-2",children:[s.jsx(pn,{className:"w-4 h-4 text-accent-primary"}),"Enhanced Prompt"]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("div",{className:"p-3 bg-card rounded-lg border border-matte",children:s.jsx("p",{className:"text-sm text-gray-300 leading-relaxed",children:C})}),s.jsxs("div",{className:"flex gap-2",children:[s.jsxs("button",{onClick:_,className:"flex-1 btn-primary flex items-center justify-center gap-2",children:[s.jsx(jr,{className:"w-4 h-4"}),"Use Enhanced Prompt"]}),s.jsx("button",{onClick:()=>{g("Enhanced: "+j.slice(0,30)+"..."),y(C),l("library")},className:"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors flex items-center gap-2",title:"Save to Library",children:s.jsx(Kl,{className:"w-4 h-4"})})]})]})]})]})]})]})},fl={brightness:100,contrast:100,saturation:100,hue:0,blur:0,rotation:0,flipX:!1,flipY:!1,cropX:0,cropY:0,cropWidth:100,cropHeight:100,zoom:1,panX:0,panY:0,shadows:0,highlights:0,temperature:0,tint:0,exposure:0,textOverlays:[],drawings:[]},th=({image:e,isOpen:t,onClose:n,onSave:r})=>{var Qr,Kn;const[l,a]=N.useState(fl),[o,i]=N.useState([fl]),[u,c]=N.useState(0),[g,x]=N.useState("adjust"),[y,j]=N.useState(!1),[w,C]=N.useState({x:0,y:0}),[$,f]=N.useState(!1),[d,m]=N.useState({x:0,y:0,width:0,height:0}),[p,S]=N.useState(null),[A,M]=N.useState(null),[_,R]=N.useState(null),[T,te]=N.useState("#ffffff"),[ve,H]=N.useState(3),[oe,we]=N.useState(1),[xe,le]=N.useState(!1),[P,Q]=N.useState(["#ff0000","#ff8000","#ffff00","#80ff00","#00ff00","#00ff80","#00ffff","#0080ff","#0000ff","#8000ff","#ff00ff","#ff0080","#ffffff","#c0c0c0","#808080","#000000"]),[U,ne]=N.useState(()=>{const h=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec("#ffffff");return h?{r:parseInt(h[1],16),g:parseInt(h[2],16),b:parseInt(h[3],16)}:{r:255,g:255,b:255}}),q=N.useRef(null),J=N.useRef(null),V=N.useRef(null),Y=N.useCallback(h=>{const v=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(h);return v?{r:parseInt(v[1],16),g:parseInt(v[2],16),b:parseInt(v[3],16)}:{r:0,g:0,b:0}},[]),je=N.useCallback((h,v,O)=>{const G=D=>{const W=Math.max(0,Math.min(255,Math.round(D))).toString(16);return W.length===1?"0"+W:W};return`#${G(h)}${G(v)}${G(O)}`},[]),vt=N.useCallback(h=>/^#[0-9A-F]{6}$/i.test(h),[]),qt=N.useCallback(h=>{vt(h)&&Q(v=>{const O=v.filter(G=>G!==h);return[h,...O].slice(0,16)})},[vt]),hs=[{name:"Original",values:{brightness:100,contrast:100,saturation:100,hue:0,blur:0}},{name:"Vintage",values:{brightness:110,contrast:120,saturation:80,hue:10,blur:.5}},{name:"Sepia",values:{brightness:110,contrast:110,saturation:60,hue:30,blur:0}},{name:"Black & White",values:{brightness:100,contrast:120,saturation:0,hue:0,blur:0}},{name:"Cool",values:{brightness:105,contrast:110,saturation:120,hue:-10,blur:0}},{name:"Warm",values:{brightness:110,contrast:105,saturation:110,hue:15,blur:0}},{name:"Dramatic",values:{brightness:95,contrast:140,saturation:130,hue:0,blur:0}},{name:"Soft",values:{brightness:115,contrast:90,saturation:95,hue:5,blur:1}}];N.useEffect(()=>{if(e){const h={...fl};a(h),i([h]),c(0)}},[e]);const wt=N.useCallback(()=>{if(!q.current||!J.current||!e)return;const h=q.current,v=h.getContext("2d");if(!v)return;const O=J.current;h.width=O.naturalWidth,h.height=O.naturalHeight,v.clearRect(0,0,h.width,h.height),v.save(),v.translate(h.width/2,h.height/2),v.rotate(l.rotation*Math.PI/180),v.scale(l.flipX?-1:1,l.flipY?-1:1);const G=[`brightness(${l.brightness}%)`,`contrast(${l.contrast}%)`,`saturate(${l.saturation}%)`,`hue-rotate(${l.hue}deg)`,`blur(${l.blur}px)`];if(v.filter=G.join(" "),v.drawImage(O,-h.width/2,-h.height/2,h.width,h.height),v.restore(),l.textOverlays.forEach(D=>{v.save(),v.font=`${D.bold?"bold":""} ${D.italic?"italic":""} ${D.fontSize}px ${D.fontFamily}`,v.fillStyle=D.color,v.strokeStyle="rgba(0,0,0,0.8)",v.lineWidth=2;const W=D.x/100*h.width,K=D.y/100*h.height;v.strokeText(D.text,W,K),v.fillText(D.text,W,K),v.restore()}),l.drawings.forEach(D=>{if(D.points.length!==0){if(v.save(),v.globalAlpha=D.opacity||1,v.strokeStyle=D.color,v.lineWidth=D.strokeWidth,v.lineCap="round",v.lineJoin="round",D.type==="brush")v.beginPath(),v.moveTo(D.points[0].x,D.points[0].y),D.points.forEach(W=>{v.lineTo(W.x,W.y)}),v.stroke();else if(D.type==="rectangle"){const W=D.points[0],K=D.points[D.points.length-1],_e=K.x-W.x,b=K.y-W.y;D.fill?(v.fillStyle=D.color,v.fillRect(W.x,W.y,_e,b)):v.strokeRect(W.x,W.y,_e,b)}else if(D.type==="circle"){const W=D.points[0],K=D.points[D.points.length-1],_e=Math.sqrt(Math.pow(K.x-W.x,2)+Math.pow(K.y-W.y,2));v.beginPath(),v.arc(W.x,W.y,_e,0,2*Math.PI),D.fill?(v.fillStyle=D.color,v.fill()):v.stroke()}v.restore()}}),_&&_.points.length>0){if(v.save(),v.globalAlpha=_.opacity||1,v.strokeStyle=_.color,v.lineWidth=_.strokeWidth,v.lineCap="round",v.lineJoin="round",_.type==="brush")v.beginPath(),v.moveTo(_.points[0].x,_.points[0].y),_.points.forEach(D=>{v.lineTo(D.x,D.y)}),v.stroke();else if(_.type==="rectangle"){const D=_.points[0],W=_.points[_.points.length-1],K=W.x-D.x,_e=W.y-D.y;v.strokeRect(D.x,D.y,K,_e)}else if(_.type==="circle"){const D=_.points[0],W=_.points[_.points.length-1],K=Math.sqrt(Math.pow(W.x-D.x,2)+Math.pow(W.y-D.y,2));v.beginPath(),v.arc(D.x,D.y,K,0,2*Math.PI),v.stroke()}v.restore()}},[l,e]);N.useEffect(()=>{if(!e||!J.current)return;const h=J.current,v=()=>wt();return h.addEventListener("load",v),h.src=e.url,()=>{h.removeEventListener("load",v),h.onload=null,h.onerror=null}},[e,wt]),N.useEffect(()=>{wt()},[wt]),N.useEffect(()=>{if(_){const h=setTimeout(()=>{wt()},8);return()=>clearTimeout(h)}},[_,wt]),N.useEffect(()=>{const h=Y(T);(h.r!==U.r||h.g!==U.g||h.b!==U.b)&&ne(h)},[T,U.r,U.g,U.b,Y]);const Pe=h=>{const v={...l,...h};a(v);const O=o.slice(0,u+1);O.push(v),i(O),c(O.length-1)},gs=()=>{if(u>0){const h=u-1;c(h),a(o[h])}},Yn=()=>{if(u<o.length-1){const h=u+1;c(h),a(o[h])}},xs=()=>{const h={...fl};a(h),i([h]),c(0)},ys=h=>{Pe(h.values)},vs=()=>{const h={id:`text-${Date.now()}`,text:"Sample Text",x:50,y:50,fontSize:24,fontFamily:"Arial",color:"#ffffff",bold:!1,italic:!1};Pe({textOverlays:[...l.textOverlays,h]}),S(h.id)},Jt=(h,v)=>{const O=l.textOverlays.map(G=>G.id===h?{...G,...v}:G);Pe({textOverlays:O})},ws=h=>{const v=l.textOverlays.filter(O=>O.id!==h);Pe({textOverlays:v}),p===h&&S(null)},js=()=>{if(!q.current||!J.current)return;const h=q.current,v=h.getContext("2d");if(!v)return;const{x:O,y:G,width:D,height:W}=d;if(D===0||W===0)return;const K=document.createElement("canvas"),_e=K.getContext("2d");_e&&(K.width=D,K.height=W,_e.drawImage(h,O,G,D,W,0,0,D,W),h.width=D,h.height=W,v.clearRect(0,0,D,W),v.drawImage(K,0,0),f(!1),m({x:0,y:0,width:0,height:0}))},xn=h=>{if(!q.current||!J.current)return;const v=q.current,O=v.getContext("2d");if(!O)return;const G=J.current,D=Math.round(G.naturalWidth*h),W=Math.round(G.naturalHeight*h),K=document.createElement("canvas"),_e=K.getContext("2d");_e&&(K.width=v.width,K.height=v.height,_e.drawImage(v,0,0),v.width=D,v.height=W,O.clearRect(0,0,D,W),O.drawImage(G,0,0,D,W),wt())},Ns=()=>{if(!q.current)return;const v=q.current.toDataURL("image/png"),O={brightness:l.brightness,contrast:l.contrast,saturation:l.saturation,hue:l.hue,blur:l.blur,rotation:l.rotation,flipX:l.flipX,flipY:l.flipY};r(v,"filters_and_transform",O),n()},Wr=h=>{const v=q.current;if(!v)return{x:0,y:0};const O=v.getBoundingClientRect(),G=h.clientX-O.left,D=h.clientY-O.top,W=v.width/O.width,K=v.height/O.height;return{x:G*W,y:D*K}},ks=h=>{var D;const v=(D=V.current)==null?void 0:D.getBoundingClientRect();if(!v)return;const O=h.clientX-v.left,G=h.clientY-v.top;if($)j(!0),C({x:O,y:G}),m({x:O,y:G,width:0,height:0});else if(A&&g==="draw"){const W=Wr(h),K={id:`drawing-${Date.now()}`,type:A,points:[W],color:T,strokeWidth:ve,opacity:oe,fill:!1};R(K),j(!0)}else j(!0),C({x:h.clientX,y:h.clientY})},Cs=h=>{var D;const v=(D=V.current)==null?void 0:D.getBoundingClientRect();if(!v)return;const O=h.clientX-v.left,G=h.clientY-v.top;if(y)if($){const W=O-w.x,K=G-w.y;m({x:Math.min(w.x,O),y:Math.min(w.y,G),width:Math.abs(W),height:Math.abs(K)})}else if(_&&A){const W=Wr(h);if(A==="brush"){const K={..._,points:[..._.points,W]};R(K)}else{const K={..._,points:[_.points[0],W]};R(K)}}else{const W=h.clientX-w.x,K=h.clientY-w.y;Pe({panX:l.panX+W,panY:l.panY+K}),C({x:h.clientX,y:h.clientY})}},Xr=()=>{_&&A&&(Pe({drawings:[...l.drawings,_]}),R(null)),j(!1)};return!t||!e?null:s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-gray-900 rounded-lg w-full h-full max-w-7xl max-h-[90vh] flex flex-col",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[s.jsx("h2",{className:"text-xl font-semibold text-white",children:"Image Editor"}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("button",{onClick:gs,disabled:u===0,className:"p-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed",title:"Undo",children:s.jsx(Dp,{size:20})}),s.jsx("button",{onClick:Yn,disabled:u===o.length-1,className:"p-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed",title:"Redo",children:s.jsx(zp,{size:20})}),s.jsx("button",{onClick:xs,className:"px-3 py-1 text-sm bg-gray-700 hover:bg-gray-600 text-white rounded",children:"Reset"}),s.jsxs("button",{onClick:Ns,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded flex items-center gap-2",children:[s.jsx(ps,{size:16}),"Save"]}),s.jsx("button",{onClick:n,className:"p-2 text-gray-400 hover:text-white",children:s.jsx(yt,{size:20})})]})]}),s.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[s.jsxs("div",{className:"w-80 bg-gray-800 border-r border-gray-700 flex flex-col",children:[s.jsx("div",{className:"flex flex-wrap border-b border-gray-700",children:[{id:"adjust",label:"Basic",icon:Pd},{id:"advanced",label:"Advanced",icon:Lp},{id:"filters",label:"Filters",icon:Id},{id:"transform",label:"Transform",icon:Mp},{id:"crop",label:"Crop",icon:Np},{id:"text",label:"Text",icon:du},{id:"draw",label:"Draw",icon:ou},{id:"resize",label:"Resize",icon:Pp}].map(({id:h,label:v,icon:O})=>s.jsxs("button",{onClick:()=>x(h),className:`flex-1 min-w-0 p-2 flex flex-col items-center gap-1 text-xs font-medium ${g===h?"bg-blue-600 text-white":"text-gray-400 hover:text-white hover:bg-gray-700"}`,children:[s.jsx(O,{size:14}),s.jsx("span",{className:"truncate",children:v})]},h))}),s.jsxs("div",{className:"flex-1 p-4 overflow-y-auto",children:[g==="adjust"&&s.jsx("div",{className:"space-y-4",children:[{key:"brightness",label:"Brightness",min:0,max:200,step:1},{key:"contrast",label:"Contrast",min:0,max:200,step:1},{key:"saturation",label:"Saturation",min:0,max:200,step:1},{key:"hue",label:"Hue",min:-180,max:180,step:1},{key:"blur",label:"Blur",min:0,max:10,step:.1}].map(({key:h,label:v,min:O,max:G,step:D})=>s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:[v,": ",l[h],h==="brightness"||h==="contrast"||h==="saturation"?"%":"",h==="hue"?"°":"",h==="blur"?"px":""]}),s.jsx("input",{type:"range",min:O,max:G,step:D,value:l[h],onChange:W=>Pe({[h]:parseFloat(W.target.value)}),className:"w-full slider"})]},h))}),g==="advanced"&&s.jsx("div",{className:"space-y-4",children:[{key:"shadows",label:"Shadows",min:-100,max:100,step:1},{key:"highlights",label:"Highlights",min:-100,max:100,step:1},{key:"temperature",label:"Temperature",min:-100,max:100,step:1},{key:"tint",label:"Tint",min:-100,max:100,step:1},{key:"exposure",label:"Exposure",min:-100,max:100,step:1}].map(({key:h,label:v,min:O,max:G,step:D})=>s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:[v,": ",l[h]]}),s.jsx("input",{type:"range",min:O,max:G,step:D,value:l[h],onChange:W=>Pe({[h]:parseFloat(W.target.value)}),className:"w-full slider"})]},h))}),g==="filters"&&s.jsxs("div",{className:"space-y-4",children:[s.jsx("h3",{className:"text-sm font-medium text-gray-300 mb-3",children:"Preset Filters"}),s.jsx("div",{className:"grid grid-cols-2 gap-2",children:hs.map(h=>s.jsx("button",{onClick:()=>ys(h),className:"p-3 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm transition-colors",children:h.name},h.name))})]}),g==="transform"&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Rotation: ",l.rotation,"°"]}),s.jsx("input",{type:"range",min:-180,max:180,step:1,value:l.rotation,onChange:h=>Pe({rotation:parseInt(h.target.value)}),className:"w-full slider"})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("button",{onClick:()=>Pe({flipX:!l.flipX}),className:`w-full p-2 rounded text-sm font-medium ${l.flipX?"bg-blue-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:"Flip Horizontal"}),s.jsx("button",{onClick:()=>Pe({flipY:!l.flipY}),className:`w-full p-2 rounded text-sm font-medium ${l.flipY?"bg-blue-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:"Flip Vertical"})]})]}),g==="crop"&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx("button",{onClick:()=>f(!$),className:`w-full p-3 rounded text-sm font-medium ${$?"bg-blue-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:$?"Exit Crop Mode":"Enter Crop Mode"}),$&&s.jsx("button",{onClick:js,className:"w-full p-2 bg-green-600 hover:bg-green-700 text-white rounded text-sm",children:"Apply Crop"})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Zoom: ",Math.round(l.zoom*100),"%"]}),s.jsx("input",{type:"range",min:.1,max:3,step:.1,value:l.zoom,onChange:h=>Pe({zoom:parseFloat(h.target.value)}),className:"w-full slider"})]}),s.jsxs("div",{className:"flex gap-2",children:[s.jsxs("button",{onClick:()=>Pe({zoom:Math.min(3,l.zoom+.1)}),className:"flex-1 p-2 bg-gray-700 hover:bg-gray-600 text-white rounded flex items-center justify-center gap-2",children:[s.jsx(Op,{size:16}),"Zoom In"]}),s.jsxs("button",{onClick:()=>Pe({zoom:Math.max(.1,l.zoom-.1)}),className:"flex-1 p-2 bg-gray-700 hover:bg-gray-600 text-white rounded flex items-center justify-center gap-2",children:[s.jsx($p,{size:16}),"Zoom Out"]})]})]}),g==="text"&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("button",{onClick:vs,className:"w-full p-3 bg-blue-600 hover:bg-blue-700 text-white rounded flex items-center justify-center gap-2",children:[s.jsx(du,{size:16}),"Add Text"]}),l.textOverlays.length>0&&s.jsxs("div",{className:"space-y-3",children:[s.jsx("h4",{className:"text-sm font-medium text-gray-300",children:"Text Overlays"}),l.textOverlays.map(h=>s.jsxs("div",{className:"p-3 bg-gray-700 rounded space-y-2",children:[s.jsx("input",{type:"text",value:h.text,onChange:v=>Jt(h.id,{text:v.target.value}),className:"w-full p-2 bg-gray-600 text-white rounded text-sm",placeholder:"Enter text..."}),s.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-xs text-gray-400 mb-1",children:"Size"}),s.jsx("input",{type:"range",min:12,max:72,value:h.fontSize,onChange:v=>Jt(h.id,{fontSize:parseInt(v.target.value)}),className:"w-full slider"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-xs text-gray-400 mb-1",children:"Color"}),s.jsx("input",{type:"color",value:h.color,onChange:v=>Jt(h.id,{color:v.target.value}),className:"w-full h-8 bg-gray-600 rounded"})]})]}),s.jsxs("div",{className:"flex gap-2",children:[s.jsx("button",{onClick:()=>Jt(h.id,{bold:!h.bold}),className:`flex-1 p-1 rounded text-xs ${h.bold?"bg-blue-600 text-white":"bg-gray-600 text-gray-300"}`,children:"Bold"}),s.jsx("button",{onClick:()=>Jt(h.id,{italic:!h.italic}),className:`flex-1 p-1 rounded text-xs ${h.italic?"bg-blue-600 text-white":"bg-gray-600 text-gray-300"}`,children:"Italic"}),s.jsx("button",{onClick:()=>ws(h.id),className:"flex-1 p-1 bg-red-600 hover:bg-red-700 text-white rounded text-xs",children:"Delete"})]})]},h.id))]})]}),g==="draw"&&s.jsxs("div",{className:"space-y-4",children:[s.jsx("div",{className:"grid grid-cols-2 gap-2",children:[{mode:"brush",label:"Brush",icon:ou},{mode:"rectangle",label:"Rectangle",icon:zd},{mode:"circle",label:"Circle",icon:jp}].map(({mode:h,label:v,icon:O})=>s.jsxs("button",{onClick:()=>M(A===h?null:h),className:`p-3 rounded flex flex-col items-center gap-1 text-xs ${A===h?"bg-blue-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:[s.jsx(O,{size:16}),v]},h))}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{children:[s.jsxs("div",{className:"flex items-center justify-between mb-2",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"Color"}),s.jsx("button",{onClick:()=>{le(!xe),xe||ne(Y(T))},className:"text-xs text-blue-400 hover:text-blue-300",children:xe?"Simple":"Advanced"})]}),xe?s.jsxs("div",{className:"space-y-3 p-3 bg-gray-700 rounded",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-xs text-gray-400 mb-2",children:"Quick Colors"}),s.jsx("div",{className:"grid grid-cols-8 gap-1",children:P.slice(0,16).map((h,v)=>s.jsx("button",{onClick:()=>{te(h),qt(h),ne(Y(h))},className:`w-6 h-6 rounded border-2 ${T===h?"border-white":"border-gray-500"}`,style:{backgroundColor:h}},v))})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("label",{className:"block text-xs text-gray-400",children:"RGB Values"}),s.jsxs("div",{className:"space-y-1",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("span",{className:"text-xs text-red-400 w-4",children:"R"}),s.jsx("input",{type:"range",min:0,max:255,value:U.r,onChange:h=>{const v=parseInt(h.target.value),O={...U,r:v};ne(O);const G=je(O.r,O.g,O.b);G!==T&&te(G)},className:"flex-1 slider"}),s.jsx("span",{className:"text-xs text-gray-400 w-8",children:U.r})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("span",{className:"text-xs text-green-400 w-4",children:"G"}),s.jsx("input",{type:"range",min:0,max:255,value:U.g,onChange:h=>{const v=parseInt(h.target.value),O={...U,g:v};ne(O);const G=je(O.r,O.g,O.b);G!==T&&te(G)},className:"flex-1 slider"}),s.jsx("span",{className:"text-xs text-gray-400 w-8",children:U.g})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("span",{className:"text-xs text-blue-400 w-4",children:"B"}),s.jsx("input",{type:"range",min:0,max:255,value:U.b,onChange:h=>{const v=parseInt(h.target.value),O={...U,b:v};ne(O);const G=je(O.r,O.g,O.b);G!==T&&te(G)},className:"flex-1 slider"}),s.jsx("span",{className:"text-xs text-gray-400 w-8",children:U.b})]})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-xs text-gray-400 mb-1",children:"Hex Color"}),s.jsx("input",{type:"text",value:T,onChange:h=>{let v=h.target.value.trim();v&&!v.startsWith("#")&&(v="#"+v),(v==="#"||/^#[0-9A-F]{0,6}$/i.test(v))&&(v.length===7?(te(v.toLowerCase()),qt(v.toLowerCase())):v.length>=1&&te(v.toLowerCase()))},onBlur:h=>{const v=h.target.value.trim();if(v.length===7&&/^#[0-9A-F]{6}$/i.test(v))te(v.toLowerCase()),qt(v.toLowerCase());else if(v.length<7&&v.length>1){const O=v+"0".repeat(7-v.length);/^#[0-9A-F]{6}$/i.test(O)&&(te(O.toLowerCase()),qt(O.toLowerCase()))}},className:"w-full p-1 bg-gray-600 text-white rounded text-xs font-mono",placeholder:"#ffffff",maxLength:7})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("span",{className:"text-xs text-gray-400",children:"Preview:"}),s.jsx("div",{className:"w-8 h-8 rounded border border-gray-500",style:{backgroundColor:T}})]})]}):s.jsx("input",{type:"color",value:T,onChange:h=>{te(h.target.value),qt(h.target.value),ne(Y(h.target.value))},className:"w-full h-10 bg-gray-600 rounded"})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Stroke Width: ",ve,"px"]}),s.jsx("input",{type:"range",min:1,max:20,value:ve,onChange:h=>H(parseInt(h.target.value)),className:"w-full slider"})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Opacity: ",Math.round(oe*100),"%"]}),s.jsx("input",{type:"range",min:.1,max:1,step:.1,value:oe,onChange:h=>we(parseFloat(h.target.value)),className:"w-full slider"})]})]}),l.drawings.length>0&&s.jsx("button",{onClick:()=>Pe({drawings:[]}),className:"w-full p-2 bg-red-600 hover:bg-red-700 text-white rounded text-sm",children:"Clear All Drawings"})]}),g==="resize"&&s.jsx("div",{className:"space-y-4",children:s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Resize Options"}),s.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[s.jsx("button",{onClick:()=>xn(.5),className:"p-2 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm",children:"50%"}),s.jsx("button",{onClick:()=>xn(.75),className:"p-2 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm",children:"75%"}),s.jsx("button",{onClick:()=>xn(1.25),className:"p-2 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm",children:"125%"}),s.jsx("button",{onClick:()=>xn(2),className:"p-2 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm",children:"200%"})]})]}),s.jsxs("div",{className:"p-3 bg-gray-700 rounded",children:[s.jsx("p",{className:"text-xs text-gray-400 mb-2",children:"Current Size"}),s.jsxs("p",{className:"text-sm text-white",children:[((Qr=q.current)==null?void 0:Qr.width)||0," × ",((Kn=q.current)==null?void 0:Kn.height)||0," pixels"]})]})]})})]})]}),s.jsx("div",{className:"flex-1 bg-gray-900 flex items-center justify-center p-4",children:s.jsxs("div",{ref:V,className:"relative max-w-full max-h-full overflow-hidden rounded-lg bg-gray-800",onMouseDown:ks,onMouseMove:Cs,onMouseUp:Xr,onMouseLeave:Xr,children:[s.jsx("canvas",{ref:q,className:"max-w-full max-h-full object-contain cursor-move",style:{transform:`scale(${l.zoom}) translate(${l.panX}px, ${l.panY}px)`}}),$&&s.jsx("div",{className:"absolute border-2 border-white border-dashed bg-black bg-opacity-30",style:{left:d.x,top:d.y,width:d.width,height:d.height,pointerEvents:"none"}}),l.textOverlays.map(h=>s.jsx("div",{className:"absolute pointer-events-none",style:{left:`${h.x}%`,top:`${h.y}%`,fontSize:`${h.fontSize}px`,fontFamily:h.fontFamily,color:h.color,fontWeight:h.bold?"bold":"normal",fontStyle:h.italic?"italic":"normal",textShadow:"1px 1px 2px rgba(0,0,0,0.8)",transform:`scale(${l.zoom})`},children:h.text},h.id)),s.jsx("img",{ref:J,src:e.url,alt:"Original",className:"hidden",crossOrigin:"anonymous"})]})})]})]})})},nh=({images:e,isOpen:t,onClose:n,onDownload:r,onDelete:l,onEdit:a})=>{const[o,i]=N.useState([]),[u,c]=N.useState([]),[g,x]=N.useState(1),[y,j]=N.useState(0),[w,C]=N.useState(0);N.useEffect(()=>{if(t&&e.length>0){const p=e.filter(S=>!S.isLoading);c(p),p.length>=2?i([p[0],p[1]]):p.length===1?i([p[0]]):i([]),x(1),j(0),C(0)}},[t,e]),N.useEffect(()=>{const p=S=>{S.key==="Escape"&&n()};return t&&(document.addEventListener("keydown",p),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",p),document.body.style.overflow="unset"}},[t,n]);const $=p=>{o.length<4&&!o.find(S=>S.id===p.id)&&i(S=>[...S,p])},f=p=>{i(S=>S.filter(A=>A.id!==p))},d=()=>{x(1),j(0),C(0)},m=p=>new Date(p).toLocaleString();return t?s.jsxs("div",{className:"fixed inset-0 bg-black bg-opacity-95 flex flex-col z-50",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-700 bg-gray-900",children:[s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx("h2",{className:"text-xl font-semibold text-white",children:"Image Comparison"}),s.jsxs("span",{className:"text-sm text-gray-400",children:[o.length," of 4 images selected"]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsxs("div",{className:"flex items-center gap-2 bg-gray-800 rounded-lg p-2",children:[s.jsx("button",{onClick:()=>x(p=>Math.max(.25,p-.25)),className:"p-1 text-gray-400 hover:text-white transition-colors",title:"Zoom Out",children:s.jsx(iu,{size:16})}),s.jsxs("span",{className:"text-sm text-gray-300 min-w-[60px] text-center",children:[Math.round(g*100),"%"]}),s.jsx("button",{onClick:()=>x(p=>Math.min(3,p+.25)),className:"p-1 text-gray-400 hover:text-white transition-colors",title:"Zoom In",children:s.jsx(jr,{size:16})}),s.jsx("button",{onClick:d,className:"p-1 text-gray-400 hover:text-white transition-colors ml-2",title:"Reset View",children:s.jsx(Ap,{size:16})})]}),s.jsx("button",{onClick:n,className:"p-2 text-gray-400 hover:text-white transition-colors",children:s.jsx(yt,{size:20})})]})]}),s.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[s.jsxs("div",{className:"w-80 bg-gray-800 border-r border-gray-700 flex flex-col",children:[s.jsxs("div",{className:"p-4 border-b border-gray-700",children:[s.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Available Images"}),s.jsx("p",{className:"text-sm text-gray-400",children:"Click to add images to comparison (max 4)"})]}),s.jsx("div",{className:"flex-1 overflow-y-auto p-4",children:s.jsx("div",{className:"grid grid-cols-2 gap-3",children:u.map(p=>{const S=o.find(A=>A.id===p.id);return s.jsxs("div",{className:`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${S?"border-accent-primary shadow-glow-sm":"border-transparent hover:border-gray-600"}`,onClick:()=>{S?f(p.id):$(p)},children:[s.jsx("img",{src:p.url,alt:p.prompt,className:"w-full aspect-square object-cover"}),s.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center",children:S?s.jsx("div",{className:"bg-accent-primary text-white rounded-full p-2",children:s.jsx(iu,{size:16})}):s.jsx("div",{className:"bg-white text-gray-900 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity",children:s.jsx(jr,{size:16})})}),s.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2",children:s.jsx("p",{className:"text-xs text-white truncate",children:p.prompt})})]},p.id)})})})]}),s.jsx("div",{className:"flex-1 flex flex-col",children:o.length===0?s.jsx("div",{className:"flex-1 flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-20 h-20 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(jr,{className:"w-10 h-10 text-gray-400"})}),s.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"No Images Selected"}),s.jsx("p",{className:"text-gray-400",children:"Select images from the sidebar to start comparing"})]})}):s.jsx(s.Fragment,{children:s.jsx("div",{className:"flex-1 p-4",children:s.jsx("div",{className:`grid gap-4 h-full ${o.length===1?"grid-cols-1":o.length===2?"grid-cols-2":o.length===3?"grid-cols-3":"grid-cols-2 grid-rows-2"}`,children:o.map(p=>s.jsxs("div",{className:"relative bg-gray-800 rounded-lg overflow-hidden group",children:[s.jsxs("div",{className:"relative h-full overflow-hidden",children:[s.jsx("img",{src:p.url,alt:p.prompt,className:"w-full h-full object-contain transition-transform duration-200",style:{transform:`scale(${g}) translate(${y}px, ${w}px)`},draggable:!1}),s.jsxs("div",{className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-2",children:[a&&s.jsx("button",{onClick:()=>a(p),className:"p-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors",title:"Edit",children:s.jsx(Vr,{size:16})}),s.jsx("button",{onClick:()=>r(p),className:"p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",title:"Download",children:s.jsx(ps,{size:16})}),s.jsx("button",{onClick:()=>l(p),className:"p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors",title:"Delete",children:s.jsx(Gn,{size:16})}),s.jsx("button",{onClick:()=>f(p.id),className:"p-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors",title:"Remove from comparison",children:s.jsx(yt,{size:16})})]})]}),s.jsxs("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/80 to-transparent p-3",children:[s.jsx("p",{className:"text-sm text-white font-medium mb-1 line-clamp-2",children:p.prompt}),s.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-300",children:[s.jsx("span",{className:"capitalize",children:p.service}),s.jsx("span",{children:m(p.timestamp)})]})]})]},p.id))})})})})]})]}):null},vn=["#3b82f6","#ef4444","#10b981","#f59e0b","#8b5cf6","#ec4899","#06b6d4","#84cc16","#f97316","#6366f1"],rh=({isOpen:e,onClose:t,images:n,collections:r,onCreateCollection:l,onUpdateCollection:a,onDeleteCollection:o,onAddImageToCollection:i,onRemoveImageFromCollection:u,selectedImages:c=new Set,onSelectCollection:g})=>{const[x,y]=N.useState(!1),[j,w]=N.useState(null),[C,$]=N.useState(""),[f,d]=N.useState(""),[m,p]=N.useState(vn[0]),[S,A]=N.useState(null);N.useEffect(()=>{const H=oe=>{oe.key==="Escape"&&(x||j?R():t())};return e&&(document.addEventListener("keydown",H),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",H),document.body.style.overflow="unset"}},[e,x,j,t]);const M=()=>{C.trim()&&(l({name:C.trim(),description:f.trim()||void 0,imageIds:Array.from(c),color:m}),R())},_=()=>{!j||!C.trim()||(a(j.id,{name:C.trim(),description:f.trim()||void 0,color:m,updatedAt:Date.now()}),R())},R=()=>{y(!1),w(null),$(""),d(""),p(vn[0])},T=H=>{w(H),$(H.name),d(H.description||""),p(H.color||vn[0])},te=H=>{c.forEach(oe=>{i(H,oe)})},ve=H=>n.filter(oe=>H.imageIds.includes(oe.id));return e?s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-gray-900 rounded-lg w-full h-full max-w-6xl max-h-[90vh] flex flex-col",children:[s.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-700",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(vr,{className:"w-6 h-6 text-accent-primary"}),s.jsx("h2",{className:"text-2xl font-semibold text-white",children:"Collections"}),s.jsxs("span",{className:"text-sm text-gray-400",children:["(",r.length," collection",r.length!==1?"s":"",")"]})]}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsxs("button",{onClick:()=>y(!0),className:"flex items-center gap-2 px-4 py-2 bg-accent-primary hover:bg-accent-secondary text-white rounded-lg transition-colors",children:[s.jsx(Bo,{className:"w-4 h-4"}),"New Collection"]}),s.jsx("button",{onClick:t,className:"p-2 text-gray-400 hover:text-white transition-colors",children:s.jsx(yt,{className:"w-6 h-6"})})]})]}),s.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[s.jsxs("div",{className:"w-1/3 border-r border-gray-700 flex flex-col",children:[s.jsxs("div",{className:"p-4 border-b border-gray-700",children:[s.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Your Collections"}),c.size>0&&s.jsxs("p",{className:"text-sm text-gray-400",children:[c.size," image",c.size!==1?"s":""," selected"]})]}),s.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-3",children:[r.map(H=>{const oe=ve(H),we=S===H.id;return s.jsxs("div",{className:`p-4 rounded-lg border cursor-pointer transition-all ${we?"border-accent-primary bg-accent-primary/10":"border-gray-700 hover:border-gray-600 bg-gray-800/50"}`,onClick:()=>{A(H.id),g==null||g(H)},children:[s.jsxs("div",{className:"flex items-start justify-between mb-2",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-4 h-4 rounded-full",style:{backgroundColor:H.color||vn[0]}}),s.jsx("h4",{className:"font-medium text-white",children:H.name})]}),s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("button",{onClick:xe=>{xe.stopPropagation(),T(H)},className:"p-1 text-gray-400 hover:text-white transition-colors",title:"Edit collection",children:s.jsx(Vr,{className:"w-4 h-4"})}),!H.isDefault&&s.jsx("button",{onClick:xe=>{xe.stopPropagation(),o(H.id)},className:"p-1 text-gray-400 hover:text-red-400 transition-colors",title:"Delete collection",children:s.jsx(Gn,{className:"w-4 h-4"})})]})]}),H.description&&s.jsx("p",{className:"text-sm text-gray-400 mb-2",children:H.description}),s.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-400",children:[s.jsxs("span",{className:"flex items-center gap-1",children:[s.jsx(wr,{className:"w-4 h-4"}),oe.length," image",oe.length!==1?"s":""]}),c.size>0&&s.jsxs("button",{onClick:xe=>{xe.stopPropagation(),te(H.id)},className:"px-2 py-1 text-xs bg-accent-primary hover:bg-accent-secondary text-white rounded transition-colors",children:["+ Add ",c.size]})]})]},H.id)}),r.length===0&&s.jsxs("div",{className:"text-center py-8",children:[s.jsx(vr,{className:"w-12 h-12 text-gray-600 mx-auto mb-3"}),s.jsx("p",{className:"text-gray-400",children:"No collections yet"}),s.jsx("p",{className:"text-sm text-gray-500",children:"Create your first collection to organize images"})]})]})]}),s.jsx("div",{className:"flex-1 flex flex-col",children:x||j?s.jsxs("div",{className:"p-6",children:[s.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:x?"Create New Collection":"Edit Collection"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Collection Name"}),s.jsx("input",{type:"text",value:C,onChange:H=>$(H.target.value),placeholder:"Enter collection name...",className:"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none",autoFocus:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description (Optional)"}),s.jsx("textarea",{value:f,onChange:H=>d(H.target.value),placeholder:"Enter collection description...",rows:3,className:"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none resize-none"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Color"}),s.jsx("div",{className:"flex items-center gap-2",children:vn.map(H=>s.jsx("button",{onClick:()=>p(H),className:`w-8 h-8 rounded-full border-2 transition-all ${m===H?"border-white scale-110":"border-gray-600 hover:border-gray-400"}`,style:{backgroundColor:H}},H))})]}),x&&c.size>0&&s.jsx("div",{className:"p-3 bg-gray-800 rounded-lg",children:s.jsxs("p",{className:"text-sm text-gray-300",children:[c.size," selected image",c.size!==1?"s":""," will be added to this collection"]})})]}),s.jsxs("div",{className:"flex items-center gap-3 mt-6",children:[s.jsxs("button",{onClick:x?M:_,disabled:!C.trim(),className:"flex items-center gap-2 px-4 py-2 bg-accent-primary hover:bg-accent-secondary disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors",children:[s.jsx(Kl,{className:"w-4 h-4"}),x?"Create Collection":"Save Changes"]}),s.jsx("button",{onClick:R,className:"px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors",children:"Cancel"})]})]}):S?s.jsx("div",{className:"flex-1 p-6",children:(()=>{const H=r.find(we=>we.id===S);if(!H)return null;const oe=ve(H);return s.jsxs("div",{children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx("div",{className:"w-6 h-6 rounded-full",style:{backgroundColor:H.color||vn[0]}}),s.jsx("h3",{className:"text-xl font-semibold text-white",children:H.name}),s.jsxs("span",{className:"text-sm text-gray-400",children:["(",oe.length," image",oe.length!==1?"s":"",")"]})]}),H.description&&s.jsx("p",{className:"text-gray-300 mb-6",children:H.description}),oe.length>0?s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:oe.map(we=>s.jsxs("div",{className:"relative group",children:[s.jsx("img",{src:we.url,alt:we.prompt,className:"w-full aspect-square object-cover rounded-lg"}),s.jsx("button",{onClick:()=>u(H.id,we.id),className:"absolute top-2 right-2 p-1 bg-red-600 hover:bg-red-700 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity",title:"Remove from collection",children:s.jsx(yt,{className:"w-4 h-4"})}),s.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2 rounded-b-lg",children:s.jsx("p",{className:"text-xs text-white truncate",children:we.prompt})})]},we.id))}):s.jsxs("div",{className:"text-center py-12",children:[s.jsx(wr,{className:"w-16 h-16 text-gray-600 mx-auto mb-4"}),s.jsx("p",{className:"text-gray-400 mb-2",children:"This collection is empty"}),s.jsx("p",{className:"text-sm text-gray-500",children:"Select images from your gallery and add them to this collection"})]})]})})()}):s.jsx("div",{className:"flex-1 flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx(vr,{className:"w-20 h-20 text-gray-600 mx-auto mb-4"}),s.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"Select a Collection"}),s.jsx("p",{className:"text-gray-400",children:"Choose a collection from the left to view its contents"})]})})})]})]})}):null};class lh extends N.Component{constructor(n){super(n);Gr(this,"handleReset",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})});this.state={hasError:!1}}static getDerivedStateFromError(n){return{hasError:!0,error:n}}componentDidCatch(n,r){console.error("Error caught by boundary:",n,r),this.setState({error:n,errorInfo:r})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:s.jsx("div",{className:"min-h-screen bg-app flex items-center justify-center p-8",children:s.jsxs("div",{className:"max-w-md w-full bg-card rounded-lg border border-matte p-6 text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-red-600/20 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(pp,{className:"w-8 h-8 text-red-500"})}),s.jsx("h2",{className:"text-xl font-semibold text-white mb-2",children:"Something went wrong"}),s.jsx("p",{className:"text-gray-400 mb-6",children:"An unexpected error occurred. Please try refreshing the page or restart the application."}),this.state.error&&s.jsxs("details",{className:"mb-6 text-left",children:[s.jsx("summary",{className:"text-sm text-gray-500 cursor-pointer hover:text-gray-400 mb-2",children:"Error Details"}),s.jsxs("div",{className:"bg-gray-800 rounded p-3 text-xs text-gray-300 font-mono overflow-auto max-h-32",children:[s.jsxs("div",{className:"mb-2",children:[s.jsx("strong",{children:"Error:"})," ",this.state.error.message]}),this.state.error.stack&&s.jsxs("div",{children:[s.jsx("strong",{children:"Stack:"}),s.jsx("pre",{className:"whitespace-pre-wrap mt-1",children:this.state.error.stack})]})]})]}),s.jsxs("div",{className:"flex gap-3 justify-center",children:[s.jsxs("button",{onClick:this.handleReset,className:"flex items-center gap-2 px-4 py-2 bg-accent-primary hover:bg-accent-secondary text-white rounded-lg transition-colors",children:[s.jsx(Xa,{className:"w-4 h-4"}),"Try Again"]}),s.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors",children:"Reload App"})]})]})}):this.props.children}}const sh=({toast:e,onClose:t})=>{const[n,r]=N.useState(!1);N.useEffect(()=>{r(!0);const o=setTimeout(()=>{r(!1),setTimeout(()=>t(e.id),300)},e.duration||4e3);return()=>clearTimeout(o)},[e.id,e.duration,t]);const l=()=>{switch(e.type){case"success":return s.jsx(xp,{className:"w-5 h-5 text-green-500"});case"error":return s.jsx(Rp,{className:"w-5 h-5 text-red-500"});case"warning":return s.jsx(Ho,{className:"w-5 h-5 text-yellow-500"})}},a=()=>{switch(e.type){case"success":return"bg-green-900/30 border-green-500/40 shadow-glow-sm";case"error":return"bg-red-900/30 border-red-500/40 shadow-glow-sm";case"warning":return"bg-yellow-900/30 border-yellow-500/40 shadow-glow-sm"}};return s.jsx("div",{className:`
        fixed top-4 right-4 z-50 max-w-sm w-full
        transform transition-all duration-300 ease-in-out
        ${n?"translate-x-0 opacity-100":"translate-x-full opacity-0"}
      `,children:s.jsx("div",{className:`p-4 rounded-2xl border ${a()} backdrop-blur-md glass-dark`,children:s.jsxs("div",{className:"flex items-start gap-3",children:[l(),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-sm font-semibold text-white",children:e.title}),e.message&&s.jsx("p",{className:"text-xs text-gray-300 mt-1 leading-relaxed",children:e.message})]}),s.jsx("button",{onClick:()=>t(e.id),className:"text-gray-400 hover:text-white transition-all duration-200 hover:scale-110 p-1 rounded-lg hover:bg-white/10",children:s.jsx(yt,{className:"w-4 h-4"})})]})})})},ah=({toasts:e,onClose:t})=>s.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-3 pointer-events-none",children:e.map(n=>s.jsx("div",{className:"pointer-events-auto",children:s.jsx(sh,{toast:n,onClose:t})},n.id))}),oh=()=>{const[e,t]=N.useState([]),n=N.useCallback(i=>{const u=Math.random().toString(36).substring(2,11),c={...i,id:u};return t(g=>[...g,c]),setTimeout(()=>{t(g=>g.filter(x=>x.id!==u))},5e3),u},[]),r=N.useCallback(i=>{t(u=>u.filter(c=>c.id!==i))},[]),l=N.useCallback((i,u)=>n({type:"success",title:i,message:u}),[n]),a=N.useCallback((i,u)=>n({type:"error",title:i,message:u}),[n]),o=N.useCallback((i,u)=>n({type:"warning",title:i,message:u}),[n]);return{toasts:e,addToast:n,removeToast:r,success:l,error:a,warning:o}},hu={prompt:"",aspectRatio:"landscape",resolution:"1024x768",aiService:"pollinations",imagefxModel:"IMAGEN_4",pollinationsNoLogo:!0,pollinationsEnhance:!1,pollinationsSafe:!0,pollinationsPrivate:!1,pollinationsModel:"flux",imagefxCount:4};function ih(){const[e,t]=N.useState([]),[n,r]=N.useState(hu),[l,a]=N.useState(!1),[o]=N.useState(()=>new Hp),[i,u]=N.useState(null),[c,g]=N.useState(!1),[x,y]=N.useState(!1),[j,w]=N.useState(!1),[C,$]=N.useState(null),[f,d]=N.useState(!1),[m,p]=N.useState(new Set),[S,A]=N.useState(!1),[M,_]=N.useState(""),[R,T]=N.useState("all"),[te,ve]=N.useState("newest"),[H,oe]=N.useState("all"),[we,xe]=N.useState(!1),[le,P]=N.useState([]),[Q,U]=N.useState(!1),{toasts:ne,removeToast:q,success:J,error:V,warning:Y}=oh();N.useEffect(()=>{(async()=>{if(window.electronAPI)try{const k=await window.electronAPI.loadSettings();k.success&&k.settings&&r({...hu,...k.settings}),w(!0)}catch(k){console.error("Failed to load settings:",k),w(!0)}else w(!0)})(),(async()=>{if(window.electronAPI)try{const k=await window.electronAPI.loadGeneratedImages();k.success&&k.images&&t(k.images)}catch(k){console.error("Failed to load generated images:",k)}})()},[]),N.useEffect(()=>{(async()=>{if(window.electronAPI&&typeof window.electronAPI.loadCollections=="function")try{const E=await window.electronAPI.loadCollections();if(E.success&&E.collections&&E.collections.length>0){const k=E.collections,z=k.find(L=>L.isDefault);if(z){const B=e.map(ee=>ee.id).filter(ee=>!z.imageIds.includes(ee));if(B.length>0){const ee=k.map(Ne=>Ne.isDefault?{...Ne,imageIds:[...Ne.imageIds,...B],updatedAt:Date.now()}:Ne);P(ee);try{typeof window.electronAPI.saveCollections=="function"&&await window.electronAPI.saveCollections(ee)}catch(Ne){console.error("Failed to save updated default collection:",Ne)}}else P(k)}else P(k)}else{const L=[{id:"default",name:"All Images",description:"Default collection containing all images",imageIds:e.map(B=>B.id),createdAt:Date.now(),updatedAt:Date.now(),color:"#3b82f6",isDefault:!0}];P(L);try{typeof window.electronAPI.saveCollections=="function"&&await window.electronAPI.saveCollections(L)}catch(B){console.error("Failed to save default collection:",B)}}}catch(E){console.error("Failed to load collections:",E);const z={id:"default",name:"All Images",description:"Default collection containing all images",imageIds:e.map(L=>L.id),createdAt:Date.now(),updatedAt:Date.now(),color:"#3b82f6",isDefault:!0};P([z])}else{const k={id:"default",name:"All Images",description:"Default collection containing all images",imageIds:e.map(z=>z.id),createdAt:Date.now(),updatedAt:Date.now(),color:"#3b82f6",isDefault:!0};P([k])}})()},[e]),N.useEffect(()=>{if(!j)return;(async()=>{if(window.electronAPI)try{await window.electronAPI.saveSettings(n)}catch(E){console.error("Failed to save settings:",E)}})()},[n,j]),N.useEffect(()=>{n.imagefxAuth&&o.updateAuth(n.imagefxAuth)},[n.imagefxAuth,o]);const je=async b=>{var z;if(!b.trim()){Y("Empty Prompt","Please enter a description for your image");return}if(l){Y("Generation in Progress","Please wait for the current generation to complete");return}if(n.aiService==="imagefx"&&(n.imagefxCount<1||n.imagefxCount>8)){V("Invalid Settings","Number of images must be between 1 and 8 for ImageFX");return}if(n.aiService==="imagefx"&&!n.imagefxAuth){V("Authentication Required","Please configure ImageFX authentication in settings");return}if(b.trim().length<3){Y("Prompt Too Short","Please enter a more detailed description (at least 3 characters)");return}a(!0);const E=n.aiService==="imagefx"?n.imagefxCount:4,k=Array.from({length:E},(L,B)=>({id:`loading-${Date.now()}-${B}`,url:"",prompt:b,timestamp:Date.now(),service:n.aiService,isLoading:!0}));t(L=>[...k,...L]);try{if(n.aiService==="pollinations")await vt(b,k);else if(n.aiService==="imagefx"){if(!((z=n.imagefxAuth)!=null&&z.trim())){V("ImageFX Authentication Required","Please enter your ImageFX authentication token in the settings"),t(L=>L.filter(B=>!k.some(ee=>ee.id===B.id)));return}if(n.imagefxAuth.length<20){V("Invalid ImageFX Token","The authentication token appears to be too short. Please check your token."),t(L=>L.filter(B=>!k.some(ee=>ee.id===B.id)));return}await qt(b,k)}else throw new Error("Invalid AI service selected")}catch(L){console.error("Generation failed:",L),t(B=>B.filter(ee=>!k.some(Ne=>Ne.id===ee.id))),V("Generation Failed",L instanceof Error?L.message:"Unknown error occurred")}finally{a(!1)}},vt=async(b,E)=>{const[k,z]=n.resolution.split("x").map(Number);let L=0;const B=E.map(async(ee,Ne)=>{try{const be=n.pollinationsSeed!==void 0?n.pollinationsSeed+Ne:Math.floor(Math.random()*1e6),ie=new URLSearchParams({width:k.toString(),height:z.toString(),seed:be.toString(),model:n.pollinationsModel,...n.pollinationsNoLogo&&{nologo:"true"},...n.pollinationsEnhance&&{enhance:"true"},...n.pollinationsSafe&&{safe:"true"},...n.pollinationsPrivate&&{private:"true"}}),Zn=`${window.electronAPI?"https://image.pollinations.ai":"/api/pollinations"}/prompt/${encodeURIComponent(b)}?${ie.toString()}`;await new Promise((He,ke)=>{const en=new Image;let Xo=!1;const Ss=()=>{Xo||(Xo=!0,en.onload=null,en.onerror=null,en.src="")},Qo=setTimeout(()=>{Ss(),ke(new Error("Image load timeout (30s)"))},3e4);en.onload=()=>{clearTimeout(Qo),Ss(),He()},en.onerror=Md=>{clearTimeout(Qo),Ss(),console.error("Image load error for URL:",Zn,Md),ke(new Error(`Failed to load image from ${Zn}. This might be a CORS issue in development mode.`))},en.crossOrigin="anonymous",en.src=Zn});const zt={id:`pollinations-${Date.now()}-${Ne}-${Math.random().toString(36).substring(2,11)}`,url:Zn,prompt:b,timestamp:Date.now(),service:"pollinations"};if(window.electronAPI)try{const He=await window.electronAPI.saveGeneratedImage(Zn,zt);He.success&&(zt.filename=He.filename)}catch(He){console.error("Failed to save generated image:",He)}t(He=>He.map(ke=>ke.id===ee.id?zt:ke));const Wo=le.find(He=>He.isDefault);if(Wo&&!Wo.imageIds.includes(zt.id)&&(P(He=>He.map(ke=>ke.isDefault&&!ke.imageIds.includes(zt.id)?{...ke,imageIds:[...ke.imageIds,zt.id],updatedAt:Date.now()}:ke)),window.electronAPI)){const He=le.map(ke=>ke.isDefault&&!ke.imageIds.includes(zt.id)?{...ke,imageIds:[...ke.imageIds,zt.id],updatedAt:Date.now()}:ke);window.electronAPI.saveCollections(He).catch(ke=>{console.error("Failed to save collections after adding new image:",ke)})}L++}catch(be){console.error(`Failed to generate image ${Ne}:`,be),t(ie=>ie.filter(Vo=>Vo.id!==ee.id))}});await Promise.allSettled(B),L>0?J("Images Generated",`Successfully generated ${L} image${L>1?"s":""}`):V("Generation Failed","No images could be generated. Please try again.")},qt=async(b,E)=>{try{if(!n.imagefxAuth)throw new Error("ImageFX authentication token is required");if(!o.isConfigured())throw new Error("ImageFX service not configured");const k=await o.generateImages({prompt:b,authToken:n.imagefxAuth,count:n.imagefxCount,model:n.imagefxModel||"IMAGEN_4",aspectRatio:Bp(n.aspectRatio),seed:n.imagefxSeed!==void 0?n.imagefxSeed:Math.floor(Math.random()*1e6)});for(let L=0;L<k.length;L++){const B=k[L],ee=E[L];if(ee){if(window.electronAPI)try{const be=await window.electronAPI.saveGeneratedImage(B.url,B);be.success&&(B.filename=be.filename)}catch(be){console.error("Failed to save generated image:",be)}t(be=>be.map(ie=>ie.id===ee.id?B:ie));const Ne=le.find(be=>be.isDefault);if(Ne&&!Ne.imageIds.includes(B.id)&&(P(be=>be.map(ie=>ie.isDefault&&!ie.imageIds.includes(B.id)?{...ie,imageIds:[...ie.imageIds,B.id],updatedAt:Date.now()}:ie)),window.electronAPI)){const be=le.map(ie=>ie.isDefault&&!ie.imageIds.includes(B.id)?{...ie,imageIds:[...ie.imageIds,B.id],updatedAt:Date.now()}:ie);window.electronAPI.saveCollections(be).catch(ie=>{console.error("Failed to save collections after adding new image:",ie)})}}}const z=E.slice(k.length);z.length>0&&t(L=>L.filter(B=>!z.some(ee=>ee.id===B.id))),k.length>0&&J("ImageFX Generation Complete",`Successfully generated ${k.length} image${k.length>1?"s":""}`)}catch(k){console.error("ImageFX generation failed:",k),t(z=>z.filter(L=>!E.some(B=>B.id===L.id))),V("ImageFX Generation Failed",k instanceof Error?k.message:"Unknown error occurred")}},hs=b=>{u(b),g(!0)},wt=()=>{g(!1),u(null)},Pe=()=>{y(b=>!b)},gs=b=>{r(E=>({...E,prompt:b})),J("Prompt Applied","The selected prompt has been applied to the input field")},Yn=b=>{$(b),d(!0)},xs=()=>{d(!1),$(null)},ys=async(b,E,k)=>{if(!(!C||!window.electronAPI))try{const z={id:`edited-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,originalImageId:C.id,editedUrl:b,editType:E,editParams:k,timestamp:Date.now()},L=await window.electronAPI.saveGeneratedImage(b,{...C,id:z.id,url:b,timestamp:z.timestamp});L.success?(t(B=>B.map(ee=>{if(ee.id===C.id){const Ne=ee.editedVersions||[];return{...ee,editedVersions:[...Ne,{...z,filename:L.filename}]}}return ee})),J("Edit Saved","Your edited image has been saved successfully")):V("Save Failed",L.error||"Failed to save edited image")}catch(z){console.error("Failed to save edited image:",z),V("Save Failed",z instanceof Error?z.message:"Unknown error occurred")}},vs=b=>{p(E=>{const k=new Set(E);return k.has(b)?k.delete(b):k.add(b),k})},Jt=()=>{const b=new Set(e.filter(E=>!E.isLoading).map(E=>E.id));p(b)},ws=()=>{p(new Set)},js=()=>{A(b=>!b),S&&p(new Set)},xn=async()=>{if(m.size===0){Y("No Images Selected","Please select images to download");return}const b=e.filter(z=>m.has(z.id));let E=0,k=0;for(const z of b)try{await _e(z),E++}catch(L){console.error(`Failed to download image ${z.id}:`,L),k++}E>0?J("Batch Download Complete",`Successfully downloaded ${E} image${E>1?"s":""}${k>0?`, ${k} failed`:""}`):V("Batch Download Failed","No images could be downloaded"),p(new Set),A(!1)},Ns=async()=>{if(m.size===0){Y("No Images Selected","Please select images to delete");return}const b=e.filter(L=>m.has(L.id));let E=0,k=0;const z=[];for(const L of b)try{window.electronAPI&&L.filename?(await window.electronAPI.deleteGeneratedImage(L.filename)).success?(E++,z.push(L.id)):k++:L.filename?k++:(E++,z.push(L.id))}catch(B){console.error(`Failed to delete image ${L.id}:`,B),k++}if(z.length>0){t(B=>B.filter(ee=>!z.includes(ee.id)));const L=le.map(B=>({...B,imageIds:B.imageIds.filter(ee=>!z.includes(ee)),updatedAt:Date.now()}));if(P(L),window.electronAPI)try{await window.electronAPI.saveCollections(L)}catch(B){console.error("Failed to update collections after batch deletion:",B)}}E>0?J("Batch Delete Complete",`Successfully deleted ${E} image${E>1?"s":""}${k>0?`, ${k} failed`:""}`):V("Batch Delete Failed","No images could be deleted"),p(new Set),A(!1)},Wr=async()=>{if(m.size===0){Y("No Images Selected","Please select images to export");return}if(!window.electronAPI){V("Export Failed","Desktop functionality not available");return}try{const b=e.filter(z=>m.has(z.id)),E=b.map(z=>({id:z.id,url:z.url,prompt:z.prompt,timestamp:z.timestamp,service:z.service,filename:z.filename})),k=await window.electronAPI.exportImages(E);k.success?J("Export Complete",`Exported ${b.length} images to: ${k.path}`):V("Export Failed",k.error||"Unknown error occurred")}catch(b){console.error("Export failed:",b),V("Export Failed",b instanceof Error?b.message:"Unknown error occurred")}p(new Set),A(!1)},ks=()=>{xe(!0)},Cs=()=>{xe(!1)},Xr=()=>{U(!0)},Qr=()=>{U(!1)},Kn=async b=>{var E;try{if(!((E=b.name)!=null&&E.trim())){V("Invalid Collection Name","Collection name cannot be empty");return}if(le.find(B=>B.name.toLowerCase()===b.name.trim().toLowerCase())){V("Duplicate Collection Name",`A collection named "${b.name}" already exists`);return}const z={...b,name:b.name.trim(),id:`collection-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,createdAt:Date.now(),updatedAt:Date.now()},L=[...le,z];if(P(L),window.electronAPI&&typeof window.electronAPI.saveCollections=="function")try{await window.electronAPI.saveCollections(L),J("Collection Created",`"${z.name}" has been created successfully`)}catch(B){console.error("Failed to save collections:",B),P(le),V("Save Failed","Failed to save collection. Please try again.")}else J("Collection Created",`"${z.name}" has been created successfully`)}catch(k){console.error("Failed to create collection:",k),V("Collection Error","Failed to create collection. Please try again.")}},h=async(b,E)=>{var k;try{if(!le.find(B=>B.id===b)){V("Collection Error","Collection not found");return}if(E.name!==void 0){if(!((k=E.name)!=null&&k.trim())){V("Invalid Collection Name","Collection name cannot be empty");return}if(le.find(ee=>ee.id!==b&&ee.name.toLowerCase()===E.name.trim().toLowerCase())){V("Duplicate Collection Name",`A collection named "${E.name}" already exists`);return}}const L=le.map(B=>B.id===b?{...B,...E,...E.name&&{name:E.name.trim()},updatedAt:Date.now()}:B);if(P(L),window.electronAPI&&typeof window.electronAPI.saveCollections=="function")try{await window.electronAPI.saveCollections(L),J("Collection Updated","Collection has been updated successfully")}catch(B){console.error("Failed to save collections:",B),P(le),V("Save Failed","Failed to save collection changes. Please try again.")}else J("Collection Updated","Collection has been updated successfully")}catch(z){console.error("Failed to update collection:",z),V("Collection Error","Failed to update collection. Please try again.")}},v=async b=>{try{const E=le.find(z=>z.id===b);if(!E){V("Collection Error","Collection not found");return}if(E.isDefault){V("Cannot Delete",'The default "All Images" collection cannot be deleted');return}const k=le.filter(z=>z.id!==b);if(P(k),window.electronAPI&&typeof window.electronAPI.saveCollections=="function")try{await window.electronAPI.saveCollections(k),J("Collection Deleted",`"${E.name}" has been deleted`)}catch(z){console.error("Failed to save collections:",z),P(le),V("Save Failed","Failed to delete collection. Please try again.")}else J("Collection Deleted",`"${E.name}" has been deleted`)}catch(E){console.error("Failed to delete collection:",E),V("Collection Error","Failed to delete collection. Please try again.")}},O=async(b,E)=>{try{const k=le.find(L=>L.id===b);if(!k){V("Collection Error","Collection not found");return}if(k.imageIds.includes(E)){Y("Already Added",`Image is already in "${k.name}"`);return}const z=le.map(L=>L.id===b?{...L,imageIds:[...L.imageIds,E],updatedAt:Date.now()}:L);if(P(z),k.isDefault||J("Added to Collection",`Image added to "${k.name}"`),window.electronAPI&&typeof window.electronAPI.saveCollections=="function")try{await window.electronAPI.saveCollections(z)}catch(L){console.error("Failed to save collections:",L),P(le),V("Save Failed","Failed to save collection changes")}}catch(k){console.error("Failed to add image to collection:",k),V("Collection Error","Failed to add image to collection")}},G=async(b,E)=>{try{const k=le.map(z=>z.id===b?{...z,imageIds:z.imageIds.filter(L=>L!==E),updatedAt:Date.now()}:z);if(P(k),window.electronAPI&&typeof window.electronAPI.saveCollections=="function")try{await window.electronAPI.saveCollections(k)}catch(z){console.error("Failed to save collections:",z),P(le),V("Save Failed","Failed to save collection changes")}}catch(k){console.error("Failed to remove image from collection:",k),V("Collection Error","Failed to remove image from collection")}},D=N.useMemo(()=>{let b=[...e];if(M.trim()){const E=M.toLowerCase();b=b.filter(k=>k.prompt.toLowerCase().includes(E)||k.service.toLowerCase().includes(E))}if(R!=="all"&&(b=b.filter(E=>E.service===R)),H!=="all"){const E=new Date,k=new Date;switch(H){case"today":k.setHours(0,0,0,0);break;case"week":k.setDate(E.getDate()-7);break;case"month":k.setMonth(E.getMonth()-1);break}b=b.filter(z=>new Date(z.timestamp)>=k)}return b.sort((E,k)=>{switch(te){case"newest":return k.timestamp-E.timestamp;case"oldest":return E.timestamp-k.timestamp;case"prompt":return E.prompt.localeCompare(k.prompt);default:return k.timestamp-E.timestamp}}),b},[e,M,R,H,te]),W=async()=>{if(window.electronAPI)try{const b=await window.electronAPI.refreshGeneratedImages();b.success&&b.images?(t(b.images),J("Images Refreshed","Image gallery has been updated")):V("Refresh Failed",b.error||"Unknown error occurred")}catch(b){console.error("Refresh failed:",b),V("Refresh Failed",b instanceof Error?b.message:"Unknown error occurred")}},K=async b=>{if(!b){V("Delete Failed","Invalid image data");return}if(!window.electronAPI){V("Delete Failed","Desktop functionality not available");return}if(!b.filename){t(k=>k.filter(z=>z.id!==b.id));const E=le.map(k=>({...k,imageIds:k.imageIds.filter(z=>z!==b.id),updatedAt:Date.now()}));P(E),J("Image Removed","Image has been removed from the gallery");return}try{const E=await window.electronAPI.deleteGeneratedImage(b.filename);if(E.success){t(z=>z.filter(L=>L.id!==b.id));const k=le.map(z=>({...z,imageIds:z.imageIds.filter(L=>L!==b.id),updatedAt:Date.now()}));if(P(k),window.electronAPI)try{await window.electronAPI.saveCollections(k)}catch(z){console.error("Failed to update collections after image deletion:",z)}J("Image Deleted","Image has been deleted successfully")}else V("Delete Failed",E.error||"Unknown error occurred")}catch(E){console.error("Delete failed:",E),V("Delete Failed",E instanceof Error?E.message:"Unknown error occurred")}},_e=async b=>{if(!window.electronAPI){V("Download Failed","Desktop functionality not available");return}try{let E;if(b.url.startsWith("data:image/"))E=b.url;else{const B=await fetch(b.url);if(!B.ok)throw new Error(`Failed to fetch image: ${B.statusText}`);const ee=await B.blob();E=await new Promise((Ne,be)=>{const ie=new FileReader;ie.onload=()=>Ne(ie.result),ie.onerror=()=>be(new Error("Failed to read image data")),ie.readAsDataURL(ee)})}const k=new Date().toISOString().replace(/[:.]/g,"-").slice(0,-5),z=`imagen-${b.service}-${k}.png`,L=await window.electronAPI.downloadImage(E,z);L.success?J("Image Downloaded",`Saved to: ${L.path}`):V("Download Failed",L.error||"Unknown error occurred")}catch(E){console.error("Download failed:",E),V("Download Failed",E instanceof Error?E.message:"Unknown error occurred")}};return s.jsx(lh,{children:s.jsxs("div",{className:"flex h-screen bg-app text-white relative",children:[s.jsx(Gp,{settings:n,onSettingsChange:r,onGenerate:je,isGenerating:l}),s.jsx("div",{className:`flex-1 h-full transition-all duration-300 relative ${x?"lg:mr-96 mr-0":"mr-0"}`,children:s.jsx(qp,{images:D,totalImages:e.length,onDownload:_e,onDelete:K,onPreview:hs,onRefresh:W,onEdit:Yn,selectedImages:m,isSelectionMode:S,onToggleSelection:vs,onToggleSelectionMode:js,onSelectAll:Jt,onDeselectAll:ws,onBatchDownload:xn,onBatchDelete:Ns,onBatchExport:Wr,searchQuery:M,onSearchChange:_,filterService:R,onFilterServiceChange:T,sortBy:te,onSortChange:ve,dateFilter:H,onDateFilterChange:oe,onOpenComparison:ks,onOpenCollections:Xr,collections:le,onAddToCollection:O,onCreateCollection:Kn})}),s.jsx(eh,{isOpen:x,onToggle:Pe,onPromptSelect:gs}),s.jsx(Jp,{image:i,isOpen:c,onClose:wt,onDownload:_e,onDelete:K,onEdit:Yn}),s.jsx(th,{image:C,isOpen:f,onClose:xs,onSave:ys}),s.jsx(nh,{images:e,isOpen:we,onClose:Cs,onDownload:_e,onDelete:K,onEdit:Yn}),s.jsx(rh,{isOpen:Q,onClose:Qr,images:e,collections:le,onCreateCollection:Kn,onUpdateCollection:h,onDeleteCollection:v,onAddImageToCollection:O,onRemoveImageFromCollection:G,selectedImages:m}),s.jsx(ah,{toasts:ne,onClose:q})]})})}Js.createRoot(document.getElementById("root")).render(s.jsx(Jd.StrictMode,{children:s.jsx(ih,{})}));
