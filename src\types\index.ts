export interface GeneratedImage {
  id: string
  url: string
  prompt: string
  timestamp: number
  service: 'pollinations' | 'imagefx'
  isLoading?: boolean
  filename?: string
  editedVersions?: EditedImageVersion[]
}

export interface EditedImageVersion {
  id: string
  originalImageId: string
  editedUrl: string
  editType: string
  editParams: Record<string, any>
  timestamp: number
}

export interface ImageCollection {
  id: string
  name: string
  description?: string
  imageIds: string[]
  createdAt: number
  updatedAt: number
  color?: string
  isDefault?: boolean
}

export interface AppSettings {
  prompt?: string
  aspectRatio: AspectRatio
  resolution: Resolution
  aiService: AIService
  imagefxAuth?: string
  imagefxModel?: string
  // Pollinations settings
  pollinationsSeed?: number
  pollinationsNoLogo: boolean
  pollinationsEnhance: boolean
  pollinationsSafe: boolean
  pollinationsPrivate: boolean
  pollinationsModel: PollinationsModel
  // ImageFX settings
  imagefxSeed?: number
  imagefxCount: number
}

export type AspectRatio = 'square' | 'portrait' | 'landscape' | 'landscape_4_3' | 'portrait_3_4'
export type Resolution = '512x512' | '768x768' | '1024x1024' | '1024x768' | '768x1024' | '1536x1024' | '1024x1536'
export type AIService = 'pollinations' | 'imagefx'
export type PollinationsModel = 'flux' | 'turbo' | 'flux-realism' | 'flux-cablyai' | 'flux-anime' | 'flux-3d' | 'any-dark'

// Chat types
export interface ChatMessage {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: number
  model?: string
  isStreaming?: boolean
}

export interface ChatSession {
  id: string
  title: string
  messages: ChatMessage[]
  createdAt: number
  updatedAt: number
  model: AIModel
}

export type AIModel =
  | 'deepseek-reasoner'
  | 'gpt-4.1'
  | 'gpt-4o'
  | 'google/gemini-2.5-pro'
  | 'google/gemini-2.5-pro-preview-05-06'

// Re-export Electron types
export type { ElectronAPI } from './electron'
