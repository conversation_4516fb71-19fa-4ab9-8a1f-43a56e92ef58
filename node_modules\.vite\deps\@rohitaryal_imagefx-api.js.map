{"version": 3, "sources": ["../../@rohitaryal/imagefx-api/dist/utils/request.js", "../../@rohitaryal/imagefx-api/dist/index.js"], "sourcesContent": ["const request = async function (req) {\n    req.headers.set(\"Origin\", \"https://labs.google\");\n    req.headers.set(\"Referer\", \"https://labs.google\");\n    try {\n        const reqs = await fetch(req.url, {\n            body: req.body,\n            method: req.method,\n            headers: req.headers,\n        });\n        const res = await reqs.text();\n        if (!reqs.ok) {\n            return {\n                Err: new Error(res)\n            };\n        }\n        return {\n            Ok: res,\n        };\n    }\n    catch (err) {\n        return {\n            Err: (err instanceof Error) ? err : new Error(\"Failed to fetch.\")\n        };\n    }\n};\nexport { request };\n", "import { request } from \"./utils/request.js\";\nclass ImageFx {\n    credentials;\n    constructor(credentials) {\n        // If both of them are missing\n        if (!credentials.authorizationKey && !credentials.cookie) {\n            throw new Error(\"Authorization token and <PERSON><PERSON> both are missing.\");\n        }\n        if (credentials.cookie && credentials.cookie.length < 70) {\n            throw new Error(\"Invalid cookie was provided.\");\n        }\n        this.credentials = structuredClone(credentials);\n        // Add the missing header\n        if (this.credentials.cookie && !this.credentials.cookie.startsWith(\"__Secure-next-auth.session-token=\")) {\n            this.credentials.cookie = \"__Secure-next-auth.session-token=\" + this.credentials.cookie;\n        }\n    }\n    async #checkToken() {\n        if (!this.credentials.authorizationKey && !this.credentials.cookie) {\n            return { Err: Error(\"Authorization token and <PERSON>ie both are missing.\") };\n        }\n        if (this.credentials.cookie && !this.credentials.authorizationKey) {\n            // Mutate internally\n            await this.getAuthToken(true);\n        }\n        return { Ok: true };\n    }\n    async getAuthToken(mutate = false) {\n        if (!this.credentials.cookie) {\n            return { Err: new Error(\"Cookie is required for generating auth token.\") };\n        }\n        const req = {\n            url: \"https://labs.google/fx/api/auth/session\",\n            method: \"GET\",\n            headers: new Headers({ \"Cookie\": this.credentials.cookie })\n        };\n        const resp = await request(req);\n        if (resp.Err || !resp.Ok) {\n            return { Err: resp.Err };\n        }\n        try {\n            const parsedResp = JSON.parse(resp.Ok);\n            if (!parsedResp.access_token) {\n                return { Err: Error(\"Access token is missing from response: \" + resp.Ok) };\n            }\n            if (mutate) {\n                this.credentials.authorizationKey = parsedResp.access_token;\n            }\n            return { Ok: parsedResp.access_token };\n        }\n        catch (err) {\n            return { Err: Error(\"Failed to parse response: \" + resp.Ok) };\n        }\n    }\n    /**\n    * Generate image from provided prompt\n    */\n    async generateImage(prompt) {\n        const tokenRes = await this.#checkToken();\n        if (tokenRes.Err) {\n            return { Err: tokenRes.Err };\n        }\n        // IMAGEN_3_5 is behind the scene IMAGEN_4\n        if (prompt.model == \"IMAGEN_4\") {\n            prompt.model = \"IMAGEN_3_5\";\n        }\n        // Looks really messy\n        const req = {\n            method: \"POST\",\n            body: JSON.stringify({\n                userInput: {\n                    candidatesCount: prompt.count || 4,\n                    prompts: [prompt.prompt],\n                    seed: prompt.seed || 0,\n                },\n                aspectRatio: prompt.aspectRatio || \"IMAGE_ASPECT_RATIO_LANDSCAPE\",\n                modelInput: { modelNameType: prompt.model || \"IMAGEN_3_5\" },\n                clientContext: { sessionId: \";1740658431200\", tool: \"IMAGE_FX\" },\n            }),\n            url: \"https://aisandbox-pa.googleapis.com/v1:runImageFx\",\n            headers: new Headers({ \"Authorization\": \"Bearer \" + this.credentials.authorizationKey }),\n        };\n        const res = await request(req);\n        if (res.Err || !res.Ok) {\n            return { Err: res.Err };\n        }\n        try {\n            const parsedRes = JSON.parse(res.Ok);\n            const images = parsedRes.imagePanels[0].generatedImages;\n            if (!Array.isArray(images)) {\n                return {\n                    Err: Error(\"Invalid response recieved: \" + res.Ok)\n                };\n            }\n            return { Ok: images };\n        }\n        catch (err) {\n            return { Err: Error(\"Failed to parse JSON: \" + res.Ok) };\n        }\n    }\n}\nexport default ImageFx;\n"], "mappings": ";;;;;;;AAAA,IAAM,UAAU,eAAgB,KAAK;AACjC,MAAI,QAAQ,IAAI,UAAU,qBAAqB;AAC/C,MAAI,QAAQ,IAAI,WAAW,qBAAqB;AAChD,MAAI;AACA,UAAM,OAAO,MAAM,MAAM,IAAI,KAAK;AAAA,MAC9B,MAAM,IAAI;AAAA,MACV,QAAQ,IAAI;AAAA,MACZ,SAAS,IAAI;AAAA,IACjB,CAAC;AACD,UAAM,MAAM,MAAM,KAAK,KAAK;AAC5B,QAAI,CAAC,KAAK,IAAI;AACV,aAAO;AAAA,QACH,KAAK,IAAI,MAAM,GAAG;AAAA,MACtB;AAAA,IACJ;AACA,WAAO;AAAA,MACH,IAAI;AAAA,IACR;AAAA,EACJ,SACO,KAAK;AACR,WAAO;AAAA,MACH,KAAM,eAAe,QAAS,MAAM,IAAI,MAAM,kBAAkB;AAAA,IACpE;AAAA,EACJ;AACJ;;;ACxBA;AACA,IAAM,UAAN,MAAc;AAAA,EAEV,YAAY,aAAa;AAF7B;AACI;AAGI,QAAI,CAAC,YAAY,oBAAoB,CAAC,YAAY,QAAQ;AACtD,YAAM,IAAI,MAAM,kDAAkD;AAAA,IACtE;AACA,QAAI,YAAY,UAAU,YAAY,OAAO,SAAS,IAAI;AACtD,YAAM,IAAI,MAAM,8BAA8B;AAAA,IAClD;AACA,SAAK,cAAc,gBAAgB,WAAW;AAE9C,QAAI,KAAK,YAAY,UAAU,CAAC,KAAK,YAAY,OAAO,WAAW,mCAAmC,GAAG;AACrG,WAAK,YAAY,SAAS,sCAAsC,KAAK,YAAY;AAAA,IACrF;AAAA,EACJ;AAAA,EAWA,MAAM,aAAa,SAAS,OAAO;AAC/B,QAAI,CAAC,KAAK,YAAY,QAAQ;AAC1B,aAAO,EAAE,KAAK,IAAI,MAAM,+CAA+C,EAAE;AAAA,IAC7E;AACA,UAAM,MAAM;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,SAAS,IAAI,QAAQ,EAAE,UAAU,KAAK,YAAY,OAAO,CAAC;AAAA,IAC9D;AACA,UAAM,OAAO,MAAM,QAAQ,GAAG;AAC9B,QAAI,KAAK,OAAO,CAAC,KAAK,IAAI;AACtB,aAAO,EAAE,KAAK,KAAK,IAAI;AAAA,IAC3B;AACA,QAAI;AACA,YAAM,aAAa,KAAK,MAAM,KAAK,EAAE;AACrC,UAAI,CAAC,WAAW,cAAc;AAC1B,eAAO,EAAE,KAAK,MAAM,4CAA4C,KAAK,EAAE,EAAE;AAAA,MAC7E;AACA,UAAI,QAAQ;AACR,aAAK,YAAY,mBAAmB,WAAW;AAAA,MACnD;AACA,aAAO,EAAE,IAAI,WAAW,aAAa;AAAA,IACzC,SACO,KAAK;AACR,aAAO,EAAE,KAAK,MAAM,+BAA+B,KAAK,EAAE,EAAE;AAAA,IAChE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,cAAc,QAAQ;AACxB,UAAM,WAAW,MAAM,sBAAK,mCAAL;AACvB,QAAI,SAAS,KAAK;AACd,aAAO,EAAE,KAAK,SAAS,IAAI;AAAA,IAC/B;AAEA,QAAI,OAAO,SAAS,YAAY;AAC5B,aAAO,QAAQ;AAAA,IACnB;AAEA,UAAM,MAAM;AAAA,MACR,QAAQ;AAAA,MACR,MAAM,KAAK,UAAU;AAAA,QACjB,WAAW;AAAA,UACP,iBAAiB,OAAO,SAAS;AAAA,UACjC,SAAS,CAAC,OAAO,MAAM;AAAA,UACvB,MAAM,OAAO,QAAQ;AAAA,QACzB;AAAA,QACA,aAAa,OAAO,eAAe;AAAA,QACnC,YAAY,EAAE,eAAe,OAAO,SAAS,aAAa;AAAA,QAC1D,eAAe,EAAE,WAAW,kBAAkB,MAAM,WAAW;AAAA,MACnE,CAAC;AAAA,MACD,KAAK;AAAA,MACL,SAAS,IAAI,QAAQ,EAAE,iBAAiB,YAAY,KAAK,YAAY,iBAAiB,CAAC;AAAA,IAC3F;AACA,UAAM,MAAM,MAAM,QAAQ,GAAG;AAC7B,QAAI,IAAI,OAAO,CAAC,IAAI,IAAI;AACpB,aAAO,EAAE,KAAK,IAAI,IAAI;AAAA,IAC1B;AACA,QAAI;AACA,YAAM,YAAY,KAAK,MAAM,IAAI,EAAE;AACnC,YAAM,SAAS,UAAU,YAAY,CAAC,EAAE;AACxC,UAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AACxB,eAAO;AAAA,UACH,KAAK,MAAM,gCAAgC,IAAI,EAAE;AAAA,QACrD;AAAA,MACJ;AACA,aAAO,EAAE,IAAI,OAAO;AAAA,IACxB,SACO,KAAK;AACR,aAAO,EAAE,KAAK,MAAM,2BAA2B,IAAI,EAAE,EAAE;AAAA,IAC3D;AAAA,EACJ;AACJ;AAnGA;AAgBU,gBAAW,iBAAG;AAChB,MAAI,CAAC,KAAK,YAAY,oBAAoB,CAAC,KAAK,YAAY,QAAQ;AAChE,WAAO,EAAE,KAAK,MAAM,kDAAkD,EAAE;AAAA,EAC5E;AACA,MAAI,KAAK,YAAY,UAAU,CAAC,KAAK,YAAY,kBAAkB;AAE/D,UAAM,KAAK,aAAa,IAAI;AAAA,EAChC;AACA,SAAO,EAAE,IAAI,KAAK;AACtB;AA2EJ,IAAO,eAAQ;", "names": []}