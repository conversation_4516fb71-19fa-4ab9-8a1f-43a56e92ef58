import { contextBridge, ipcRenderer } from 'electron'

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  downloadImage: (imageData: string, filename: string) =>
    ipcRenderer.invoke('download-image', imageData, filename),

  // Settings operations
  saveSettings: (settings: any) =>
    ipcRenderer.invoke('save-settings', settings),
  loadSettings: () =>
    ipcRenderer.invoke('load-settings'),

  // Export operations
  exportImages: (imageData: any[]) =>
    ipcRenderer.invoke('export-images', imageData),

  // Collections operations
  loadCollections: () =>
    ipcRenderer.invoke('load-collections'),
  saveCollections: (collections: any[]) =>
    ipcRenderer.invoke('save-collections', collections),

  // Generated images operations
  saveGeneratedImage: (imageData: string, imageInfo: any) =>
    ipcRenderer.invoke('save-generated-image', imageData, imageInfo),
  loadGeneratedImages: () =>
    ipcRenderer.invoke('load-generated-images'),
  deleteGeneratedImage: (filename: string) =>
    ipcRenderer.invoke('delete-generated-image', filename),

  // Refresh images manually
  refreshGeneratedImages: () =>
    ipcRenderer.invoke('refresh-generated-images'),

  // Listen for file system changes
  onImagesChanged: (callback: () => void) => {
    ipcRenderer.on('images-changed', callback)
    return () => ipcRenderer.removeListener('images-changed', callback)
  },

  // Prompt library operations
  savePrompts: (prompts: any[]) =>
    ipcRenderer.invoke('save-prompts', prompts),
  loadPrompts: () =>
    ipcRenderer.invoke('load-prompts'),

  // Qwen Chat operations
  openQwenChat: () =>
    ipcRenderer.invoke('open-qwen-chat'),
  clearQwenChatSession: () =>
    ipcRenderer.invoke('clear-qwen-chat-session'),

  // Chat sessions operations
  saveChatSessions: (sessions: any[]) =>
    ipcRenderer.invoke('save-chat-sessions', sessions),
  loadChatSessions: () =>
    ipcRenderer.invoke('load-chat-sessions')
})
