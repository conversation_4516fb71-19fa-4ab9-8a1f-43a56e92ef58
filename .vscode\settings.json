{"css.validate": false, "less.validate": false, "scss.validate": false, "css.customData": [".vscode/css_custom_data.json"], "tailwindCSS.includeLanguages": {"typescript": "typescript", "typescriptreact": "typescriptreact"}, "tailwindCSS.experimental.classRegex": [["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"], ["className\\s*=\\s*[\"'`]([^\"'`]*)[\"'`]", "([a-zA-Z0-9\\-:]+)"]], "files.associations": {"*.css": "tailwindcss"}}