import { useState, useEffect, useMemo } from 'react'
import Sidebar from './components/Sidebar'
import ImageGrid from './components/ImageGrid'
import ImagePreview from './components/ImagePreview'
import RightSidebar from './components/RightSidebar'
import ImageEditor from './components/ImageEditor'
import ImageComparison from './components/ImageComparison'
import Collections from './components/Collections'
import Chat from './components/Chat'
import ErrorBoundary from './components/ErrorBoundary'
import { ToastContainer } from './components/Toast'
import { GeneratedImage, AppSettings, EditedImageVersion, ImageCollection } from './types'
import { ImageFXService, mapAspectRatio } from './utils/imagefx'
import { useToast } from './hooks/useToast'
import { Image, MessageSquare } from 'lucide-react'

const defaultSettings: AppSettings = {
  prompt: '',
  aspectRatio: 'landscape',
  resolution: '1024x768',
  aiService: 'pollinations',
  imagefxModel: 'IMAGEN_4',
  // Pollinations defaults
  pollinationsNoLogo: true,
  pollinationsEnhance: false,
  pollinationsSafe: true,
  pollinationsPrivate: false,
  pollinationsModel: 'flux',
  // ImageFX defaults
  imagefxCount: 4,
}

function App() {
  const [activeTab, setActiveTab] = useState<'images' | 'chat'>('images')
  const [images, setImages] = useState<GeneratedImage[]>([])
  const [settings, setSettings] = useState<AppSettings>(defaultSettings)
  const [isGenerating, setIsGenerating] = useState(false)
  const [imageFXService] = useState(() => new ImageFXService())
  const [previewImage, setPreviewImage] = useState<GeneratedImage | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [isRightSidebarOpen, setIsRightSidebarOpen] = useState(false)
  const [settingsLoaded, setSettingsLoaded] = useState(false)
  const [editingImage, setEditingImage] = useState<GeneratedImage | null>(null)
  const [isEditorOpen, setIsEditorOpen] = useState(false)
  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set())
  const [isSelectionMode, setIsSelectionMode] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterService, setFilterService] = useState<'all' | 'pollinations' | 'imagefx'>('all')
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'prompt'>('newest')
  const [dateFilter, setDateFilter] = useState<'all' | 'today' | 'week' | 'month'>('all')
  const [isComparisonOpen, setIsComparisonOpen] = useState(false)
  const [collections, setCollections] = useState<ImageCollection[]>([])
  const [isCollectionsOpen, setIsCollectionsOpen] = useState(false)
  const { toasts, removeToast, success, error: showError, warning } = useToast()

  // Load settings from file storage on mount
  useEffect(() => {
    const loadSettings = async () => {
      if (window.electronAPI) {
        try {
          const result = await window.electronAPI.loadSettings()
          if (result.success && result.settings) {
            setSettings({ ...defaultSettings, ...result.settings })
          }
          setSettingsLoaded(true)
        } catch (error) {
          console.error('Failed to load settings:', error)
          setSettingsLoaded(true)
        }
      } else {
        setSettingsLoaded(true)
      }
    }
    loadSettings()

    // Load generated images
    const loadGeneratedImages = async () => {
      if (window.electronAPI) {
        try {
          const result = await window.electronAPI.loadGeneratedImages()
          if (result.success && result.images) {
            setImages(result.images)
          }
        } catch (error) {
          console.error('Failed to load generated images:', error)
        }
      }
    }
    loadGeneratedImages()
  }, [])

  // Load collections after images are loaded
  useEffect(() => {
    const loadCollections = async () => {
      // Check if we're in Electron environment and the API exists
      if (window.electronAPI && typeof window.electronAPI.loadCollections === 'function') {
        try {
          const result = await window.electronAPI.loadCollections()
          if (result.success && result.collections && result.collections.length > 0) {
            // Check if default collection exists and populate it with all images if needed
            const collections = result.collections
            const defaultCollection = collections.find(c => c.isDefault)

            if (defaultCollection) {
              // Get all current image IDs
              const allImageIds = images.map(img => img.id)
              // Add any missing images to the default collection
              const missingImageIds = allImageIds.filter(id => !defaultCollection.imageIds.includes(id))

              if (missingImageIds.length > 0) {
                const updatedCollections = collections.map(collection => {
                  if (collection.isDefault) {
                    return {
                      ...collection,
                      imageIds: [...collection.imageIds, ...missingImageIds],
                      updatedAt: Date.now(),
                    }
                  }
                  return collection
                })
                setCollections(updatedCollections)

                // Save the updated collections
                try {
                  if (typeof window.electronAPI.saveCollections === 'function') {
                    await window.electronAPI.saveCollections(updatedCollections)
                  }
                } catch (saveError) {
                  console.error('Failed to save updated default collection:', saveError)
                }
              } else {
                setCollections(collections)
              }
            } else {
              setCollections(collections)
            }
          } else {
            // Create default "All Images" collection if none exist
            const allImageIds = images.map(img => img.id)
            const defaultCollection: ImageCollection = {
              id: 'default',
              name: 'All Images',
              description: 'Default collection containing all images',
              imageIds: allImageIds,
              createdAt: Date.now(),
              updatedAt: Date.now(),
              color: '#3b82f6',
              isDefault: true,
            }
            const newCollections = [defaultCollection]
            setCollections(newCollections)

            // Save the default collection
            try {
              if (typeof window.electronAPI.saveCollections === 'function') {
                await window.electronAPI.saveCollections(newCollections)
              }
            } catch (saveError) {
              console.error('Failed to save default collection:', saveError)
            }
          }
        } catch (error) {
          console.error('Failed to load collections:', error)
          // Fallback to default collection even if loading fails
          const allImageIds = images.map(img => img.id)
          const defaultCollection: ImageCollection = {
            id: 'default',
            name: 'All Images',
            description: 'Default collection containing all images',
            imageIds: allImageIds,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            color: '#3b82f6',
            isDefault: true,
          }
          setCollections([defaultCollection])
        }
      } else {
        // If no electron API or in web environment, create default collection for UI consistency
        const allImageIds = images.map(img => img.id)
        const defaultCollection: ImageCollection = {
          id: 'default',
          name: 'All Images',
          description: 'Default collection containing all images',
          imageIds: allImageIds,
          createdAt: Date.now(),
          updatedAt: Date.now(),
          color: '#3b82f6',
          isDefault: true,
        }
        setCollections([defaultCollection])
      }
    }
    loadCollections()
  }, [images])

  // Save settings to file storage when they change
  useEffect(() => {
    if (!settingsLoaded) return // Don't save until settings have been loaded

    const saveSettings = async () => {
      if (window.electronAPI) {
        try {
          await window.electronAPI.saveSettings(settings)
        } catch (error) {
          console.error('Failed to save settings:', error)
        }
      }
    }
    saveSettings()
  }, [settings, settingsLoaded])

  // Update ImageFX auth when settings change
  useEffect(() => {
    if (settings.imagefxAuth) {
      imageFXService.updateAuth(settings.imagefxAuth)
    }
  }, [settings.imagefxAuth, imageFXService])

  const handleGenerate = async (prompt: string) => {
    if (!prompt.trim()) {
      warning('Empty Prompt', 'Please enter a description for your image')
      return
    }

    if (isGenerating) {
      warning('Generation in Progress', 'Please wait for the current generation to complete')
      return
    }

    // Validate settings
    if (settings.aiService === 'imagefx' && (settings.imagefxCount < 1 || settings.imagefxCount > 8)) {
      showError('Invalid Settings', 'Number of images must be between 1 and 8 for ImageFX')
      return
    }

    if (settings.aiService === 'imagefx' && !settings.imagefxAuth) {
      showError('Authentication Required', 'Please configure ImageFX authentication in settings')
      return
    }

    if (prompt.trim().length < 3) {
      warning('Prompt Too Short', 'Please enter a more detailed description (at least 3 characters)')
      return
    }

    setIsGenerating(true)

    // Create placeholder images for loading state
    const imageCount = settings.aiService === 'imagefx' ? settings.imagefxCount : 4
    const placeholderImages: GeneratedImage[] = Array.from({ length: imageCount }, (_, index) => ({
      id: `loading-${Date.now()}-${index}`,
      url: '',
      prompt,
      timestamp: Date.now(),
      service: settings.aiService,
      isLoading: true,
    }))

    setImages(prev => [...placeholderImages, ...prev])

    try {
      if (settings.aiService === 'pollinations') {
        await generateWithPollinations(prompt, placeholderImages)
      } else if (settings.aiService === 'imagefx') {
        if (!settings.imagefxAuth?.trim()) {
          showError('ImageFX Authentication Required', 'Please enter your ImageFX authentication token in the settings')
          setImages(prev => prev.filter(img => !placeholderImages.some(ph => ph.id === img.id)))
          return
        }

        // Basic validation for ImageFX token format
        if (settings.imagefxAuth.length < 20) {
          showError('Invalid ImageFX Token', 'The authentication token appears to be too short. Please check your token.')
          setImages(prev => prev.filter(img => !placeholderImages.some(ph => ph.id === img.id)))
          return
        }

        await generateWithImageFX(prompt, placeholderImages)
      } else {
        throw new Error('Invalid AI service selected')
      }
    } catch (error) {
      console.error('Generation failed:', error)
      // Remove loading placeholders on error
      setImages(prev => prev.filter(img => !placeholderImages.some(ph => ph.id === img.id)))
      showError('Generation Failed', error instanceof Error ? error.message : 'Unknown error occurred')
    } finally {
      setIsGenerating(false)
    }
  }

  const generateWithPollinations = async (prompt: string, placeholders: GeneratedImage[]) => {
    const [width, height] = settings.resolution.split('x').map(Number)
    let successCount = 0

    const promises = placeholders.map(async (placeholder, index) => {
      try {
        // Use provided seed + index for variation, or random seed
        const seed = settings.pollinationsSeed !== undefined
          ? settings.pollinationsSeed + index
          : Math.floor(Math.random() * 1000000)

        // Build URL with all parameters
        const params = new URLSearchParams({
          width: width.toString(),
          height: height.toString(),
          seed: seed.toString(),
          model: settings.pollinationsModel,
          ...(settings.pollinationsNoLogo && { nologo: 'true' }),
          ...(settings.pollinationsEnhance && { enhance: 'true' }),
          ...(settings.pollinationsSafe && { safe: 'true' }),
          ...(settings.pollinationsPrivate && { private: 'true' }),
        })

        // Use proxy in development, direct URL in production (Electron)
        const baseUrl = window.electronAPI ? 'https://image.pollinations.ai' : '/api/pollinations'
        const url = `${baseUrl}/prompt/${encodeURIComponent(prompt)}?${params.toString()}`

        // Test if image loads successfully with timeout
        await new Promise<void>((resolve, reject) => {
          const img = new Image()
          let isResolved = false

          const cleanup = () => {
            if (!isResolved) {
              isResolved = true
              img.onload = null
              img.onerror = null
              img.src = '' // Clear src to prevent memory leaks
            }
          }

          const timeout = setTimeout(() => {
            cleanup()
            reject(new Error('Image load timeout (30s)'))
          }, 30000) // 30 second timeout

          img.onload = () => {
            clearTimeout(timeout)
            cleanup()
            resolve()
          }

          img.onerror = (error) => {
            clearTimeout(timeout)
            cleanup()
            console.error('Image load error for URL:', url, error)
            reject(new Error(`Failed to load image from ${url}. This might be a CORS issue in development mode.`))
          }

          // Set crossOrigin to handle CORS issues
          img.crossOrigin = 'anonymous'
          img.src = url
        })

        const generatedImage: GeneratedImage = {
          id: `pollinations-${Date.now()}-${index}-${Math.random().toString(36).substring(2, 11)}`,
          url,
          prompt,
          timestamp: Date.now(),
          service: 'pollinations',
        }

        // Save image to file storage
        if (window.electronAPI) {
          try {
            const result = await window.electronAPI.saveGeneratedImage(url, generatedImage)
            if (result.success) {
              generatedImage.filename = result.filename
            }
          } catch (error) {
            console.error('Failed to save generated image:', error)
          }
        }

        setImages(prev => prev.map(img =>
          img.id === placeholder.id ? generatedImage : img
        ))

        // Add to default collection immediately
        const defaultCollection = collections.find(c => c.isDefault)
        if (defaultCollection && !defaultCollection.imageIds.includes(generatedImage.id)) {
          // Update collections state immediately without async call to avoid race conditions
          setCollections(prev => prev.map(collection => {
            if (collection.isDefault && !collection.imageIds.includes(generatedImage.id)) {
              return {
                ...collection,
                imageIds: [...collection.imageIds, generatedImage.id],
                updatedAt: Date.now(),
              }
            }
            return collection
          }))

          // Save to file storage in background
          if (window.electronAPI) {
            const updatedCollections = collections.map(collection => {
              if (collection.isDefault && !collection.imageIds.includes(generatedImage.id)) {
                return {
                  ...collection,
                  imageIds: [...collection.imageIds, generatedImage.id],
                  updatedAt: Date.now(),
                }
              }
              return collection
            })
            window.electronAPI.saveCollections(updatedCollections).catch(error => {
              console.error('Failed to save collections after adding new image:', error)
            })
          }
        }

        successCount++
      } catch (error) {
        console.error(`Failed to generate image ${index}:`, error)
        setImages(prev => prev.filter(img => img.id !== placeholder.id))
      }
    })

    await Promise.allSettled(promises)

    // Show success notification
    if (successCount > 0) {
      success('Images Generated', `Successfully generated ${successCount} image${successCount > 1 ? 's' : ''}`)
    } else {
      showError('Generation Failed', 'No images could be generated. Please try again.')
    }
  }

  const generateWithImageFX = async (prompt: string, placeholders: GeneratedImage[]) => {
    try {
      if (!settings.imagefxAuth) {
        throw new Error('ImageFX authentication token is required')
      }

      if (!imageFXService.isConfigured()) {
        throw new Error('ImageFX service not configured')
      }

      const generatedImages = await imageFXService.generateImages({
        prompt,
        authToken: settings.imagefxAuth,
        count: settings.imagefxCount,
        model: (settings.imagefxModel as any) || 'IMAGEN_4',
        aspectRatio: mapAspectRatio(settings.aspectRatio),
        seed: settings.imagefxSeed !== undefined ? settings.imagefxSeed : Math.floor(Math.random() * 1000000)
      })

      // Replace placeholders with generated images and save to file storage
      for (let index = 0; index < generatedImages.length; index++) {
        const generatedImage = generatedImages[index]
        const placeholder = placeholders[index]

        if (placeholder) {
          // Save image to file storage
          if (window.electronAPI) {
            try {
              const result = await window.electronAPI.saveGeneratedImage(generatedImage.url, generatedImage)
              if (result.success) {
                generatedImage.filename = result.filename
              }
            } catch (error) {
              console.error('Failed to save generated image:', error)
            }
          }

          setImages(prev => prev.map(img =>
            img.id === placeholder.id ? generatedImage : img
          ))

          // Add to default collection immediately
          const defaultCollection = collections.find(c => c.isDefault)
          if (defaultCollection && !defaultCollection.imageIds.includes(generatedImage.id)) {
            // Update collections state immediately
            setCollections(prev => prev.map(collection => {
              if (collection.isDefault && !collection.imageIds.includes(generatedImage.id)) {
                return {
                  ...collection,
                  imageIds: [...collection.imageIds, generatedImage.id],
                  updatedAt: Date.now(),
                }
              }
              return collection
            }))

            // Save to file storage in background
            if (window.electronAPI) {
              const updatedCollections = collections.map(collection => {
                if (collection.isDefault && !collection.imageIds.includes(generatedImage.id)) {
                  return {
                    ...collection,
                    imageIds: [...collection.imageIds, generatedImage.id],
                    updatedAt: Date.now(),
                  }
                }
                return collection
              })
              window.electronAPI.saveCollections(updatedCollections).catch(error => {
                console.error('Failed to save collections after adding new image:', error)
              })
            }
          }
        }
      }

      // Remove any remaining placeholders if we got fewer images
      const remainingPlaceholders = placeholders.slice(generatedImages.length)
      if (remainingPlaceholders.length > 0) {
        setImages(prev => prev.filter(img =>
          !remainingPlaceholders.some(ph => ph.id === img.id)
        ))
      }

      // Show success notification
      if (generatedImages.length > 0) {
        success('ImageFX Generation Complete', `Successfully generated ${generatedImages.length} image${generatedImages.length > 1 ? 's' : ''}`)
      }
    } catch (error) {
      console.error('ImageFX generation failed:', error)
      // Remove all placeholders on error
      setImages(prev => prev.filter(img => !placeholders.some(ph => ph.id === img.id)))
      showError('ImageFX Generation Failed', error instanceof Error ? error.message : 'Unknown error occurred')
    }
  }

  const handlePreview = (image: GeneratedImage) => {
    setPreviewImage(image)
    setIsPreviewOpen(true)
  }

  const handleClosePreview = () => {
    setIsPreviewOpen(false)
    setPreviewImage(null)
  }

  const handleToggleRightSidebar = () => {
    setIsRightSidebarOpen(prev => !prev)
  }

  const handlePromptSelect = (prompt: string) => {
    setSettings(prev => ({ ...prev, prompt }))
    success('Prompt Applied', 'The selected prompt has been applied to the input field')
  }

  const handleEdit = (image: GeneratedImage) => {
    setEditingImage(image)
    setIsEditorOpen(true)
  }

  const handleCloseEditor = () => {
    setIsEditorOpen(false)
    setEditingImage(null)
  }

  const handleSaveEdit = async (editedImageData: string, editType: string, editParams: Record<string, any>) => {
    if (!editingImage || !window.electronAPI) return

    try {
      // Create edited version info
      const editedVersion: EditedImageVersion = {
        id: `edited-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        originalImageId: editingImage.id,
        editedUrl: editedImageData,
        editType,
        editParams,
        timestamp: Date.now(),
      }

      // Save the edited image to file storage
      const result = await window.electronAPI.saveGeneratedImage(editedImageData, {
        ...editingImage,
        id: editedVersion.id,
        url: editedImageData,
        timestamp: editedVersion.timestamp,
      })

      if (result.success) {
        // Update the original image with the edited version
        setImages(prev => prev.map(img => {
          if (img.id === editingImage.id) {
            const editedVersions = img.editedVersions || []
            return {
              ...img,
              editedVersions: [...editedVersions, { ...editedVersion, filename: result.filename }]
            }
          }
          return img
        }))

        success('Edit Saved', 'Your edited image has been saved successfully')
      } else {
        showError('Save Failed', result.error || 'Failed to save edited image')
      }
    } catch (error) {
      console.error('Failed to save edited image:', error)
      showError('Save Failed', error instanceof Error ? error.message : 'Unknown error occurred')
    }
  }

  const handleToggleSelection = (imageId: string) => {
    setSelectedImages(prev => {
      const newSet = new Set(prev)
      if (newSet.has(imageId)) {
        newSet.delete(imageId)
      } else {
        newSet.add(imageId)
      }
      return newSet
    })
  }

  const handleSelectAll = () => {
    const allImageIds = new Set(images.filter(img => !img.isLoading).map(img => img.id))
    setSelectedImages(allImageIds)
  }

  const handleDeselectAll = () => {
    setSelectedImages(new Set())
  }

  const handleToggleSelectionMode = () => {
    setIsSelectionMode(prev => !prev)
    if (isSelectionMode) {
      setSelectedImages(new Set())
    }
  }

  const handleBatchDownload = async () => {
    if (selectedImages.size === 0) {
      warning('No Images Selected', 'Please select images to download')
      return
    }

    const selectedImageObjects = images.filter(img => selectedImages.has(img.id))
    let successCount = 0
    let failCount = 0

    for (const image of selectedImageObjects) {
      try {
        await handleDownload(image)
        successCount++
      } catch (error) {
        console.error(`Failed to download image ${image.id}:`, error)
        failCount++
      }
    }

    if (successCount > 0) {
      success('Batch Download Complete', `Successfully downloaded ${successCount} image${successCount > 1 ? 's' : ''}${failCount > 0 ? `, ${failCount} failed` : ''}`)
    } else {
      showError('Batch Download Failed', 'No images could be downloaded')
    }

    setSelectedImages(new Set())
    setIsSelectionMode(false)
  }

  const handleBatchDelete = async () => {
    if (selectedImages.size === 0) {
      warning('No Images Selected', 'Please select images to delete')
      return
    }

    const selectedImageObjects = images.filter(img => selectedImages.has(img.id))
    let successCount = 0
    let failCount = 0
    const deletedImageIds: string[] = []

    for (const image of selectedImageObjects) {
      try {
        if (window.electronAPI && image.filename) {
          const result = await window.electronAPI.deleteGeneratedImage(image.filename)
          if (result.success) {
            successCount++
            deletedImageIds.push(image.id)
          } else {
            failCount++
          }
        } else if (!image.filename) {
          // Images without filename can be removed from UI
          successCount++
          deletedImageIds.push(image.id)
        } else {
          failCount++
        }
      } catch (error) {
        console.error(`Failed to delete image ${image.id}:`, error)
        failCount++
      }
    }

    // Remove successfully deleted images from UI
    if (deletedImageIds.length > 0) {
      setImages(prev => prev.filter(img => !deletedImageIds.includes(img.id)))

      // Remove from all collections
      const updatedCollections = collections.map(collection => ({
        ...collection,
        imageIds: collection.imageIds.filter(id => !deletedImageIds.includes(id)),
        updatedAt: Date.now(),
      }))
      setCollections(updatedCollections)

      // Save updated collections
      if (window.electronAPI) {
        try {
          await window.electronAPI.saveCollections(updatedCollections)
        } catch (error) {
          console.error('Failed to update collections after batch deletion:', error)
        }
      }
    }

    if (successCount > 0) {
      success('Batch Delete Complete', `Successfully deleted ${successCount} image${successCount > 1 ? 's' : ''}${failCount > 0 ? `, ${failCount} failed` : ''}`)
    } else {
      showError('Batch Delete Failed', 'No images could be deleted')
    }

    setSelectedImages(new Set())
    setIsSelectionMode(false)
  }

  const handleBatchExport = async () => {
    if (selectedImages.size === 0) {
      warning('No Images Selected', 'Please select images to export')
      return
    }

    if (!window.electronAPI) {
      showError('Export Failed', 'Desktop functionality not available')
      return
    }

    try {
      const selectedImageObjects = images.filter(img => selectedImages.has(img.id))
      const exportData = selectedImageObjects.map(img => ({
        id: img.id,
        url: img.url,
        prompt: img.prompt,
        timestamp: img.timestamp,
        service: img.service,
        filename: img.filename,
      }))

      const result = await window.electronAPI.exportImages(exportData)

      if (result.success) {
        success('Export Complete', `Exported ${selectedImageObjects.length} images to: ${result.path}`)
      } else {
        showError('Export Failed', result.error || 'Unknown error occurred')
      }
    } catch (error) {
      console.error('Export failed:', error)
      showError('Export Failed', error instanceof Error ? error.message : 'Unknown error occurred')
    }

    setSelectedImages(new Set())
    setIsSelectionMode(false)
  }

  const handleOpenComparison = () => {
    setIsComparisonOpen(true)
  }

  const handleCloseComparison = () => {
    setIsComparisonOpen(false)
  }

  const handleOpenCollections = () => {
    setIsCollectionsOpen(true)
  }

  const handleCloseCollections = () => {
    setIsCollectionsOpen(false)
  }

  const handleCreateCollection = async (collectionData: Omit<ImageCollection, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      // Validate collection name
      if (!collectionData.name?.trim()) {
        showError('Invalid Collection Name', 'Collection name cannot be empty')
        return
      }

      // Check for duplicate names
      const existingCollection = collections.find(c =>
        c.name.toLowerCase() === collectionData.name.trim().toLowerCase()
      )
      if (existingCollection) {
        showError('Duplicate Collection Name', `A collection named "${collectionData.name}" already exists`)
        return
      }

      const newCollection: ImageCollection = {
        ...collectionData,
        name: collectionData.name.trim(),
        id: `collection-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      }

      const updatedCollections = [...collections, newCollection]
      setCollections(updatedCollections)

      // Save to file storage
      if (window.electronAPI && typeof window.electronAPI.saveCollections === 'function') {
        try {
          await window.electronAPI.saveCollections(updatedCollections)
          success('Collection Created', `"${newCollection.name}" has been created successfully`)
        } catch (error) {
          console.error('Failed to save collections:', error)
          // Revert the state change on save failure
          setCollections(collections)
          showError('Save Failed', 'Failed to save collection. Please try again.')
        }
      } else {
        // In web environment, just show success (collections are stored in memory)
        success('Collection Created', `"${newCollection.name}" has been created successfully`)
      }
    } catch (error) {
      console.error('Failed to create collection:', error)
      showError('Collection Error', 'Failed to create collection. Please try again.')
    }
  }

  const handleUpdateCollection = async (id: string, updates: Partial<ImageCollection>) => {
    try {
      // Validate collection exists
      const existingCollection = collections.find(c => c.id === id)
      if (!existingCollection) {
        showError('Collection Error', 'Collection not found')
        return
      }

      // Validate name if it's being updated
      if (updates.name !== undefined) {
        if (!updates.name?.trim()) {
          showError('Invalid Collection Name', 'Collection name cannot be empty')
          return
        }

        // Check for duplicate names (excluding current collection)
        const duplicateCollection = collections.find(c =>
          c.id !== id && c.name.toLowerCase() === updates.name!.trim().toLowerCase()
        )
        if (duplicateCollection) {
          showError('Duplicate Collection Name', `A collection named "${updates.name}" already exists`)
          return
        }
      }

      const updatedCollections = collections.map(collection =>
        collection.id === id ? {
          ...collection,
          ...updates,
          ...(updates.name && { name: updates.name.trim() }),
          updatedAt: Date.now()
        } : collection
      )
      setCollections(updatedCollections)

      // Save to file storage
      if (window.electronAPI && typeof window.electronAPI.saveCollections === 'function') {
        try {
          await window.electronAPI.saveCollections(updatedCollections)
          success('Collection Updated', 'Collection has been updated successfully')
        } catch (error) {
          console.error('Failed to save collections:', error)
          // Revert the state change on save failure
          setCollections(collections)
          showError('Save Failed', 'Failed to save collection changes. Please try again.')
        }
      } else {
        // In web environment, just show success (collections are stored in memory)
        success('Collection Updated', 'Collection has been updated successfully')
      }
    } catch (error) {
      console.error('Failed to update collection:', error)
      showError('Collection Error', 'Failed to update collection. Please try again.')
    }
  }

  const handleDeleteCollection = async (id: string) => {
    try {
      const collection = collections.find(c => c.id === id)
      if (!collection) {
        showError('Collection Error', 'Collection not found')
        return
      }

      if (collection.isDefault) {
        showError('Cannot Delete', 'The default "All Images" collection cannot be deleted')
        return
      }

      const updatedCollections = collections.filter(c => c.id !== id)
      setCollections(updatedCollections)

      // Save to file storage
      if (window.electronAPI && typeof window.electronAPI.saveCollections === 'function') {
        try {
          await window.electronAPI.saveCollections(updatedCollections)
          success('Collection Deleted', `"${collection.name}" has been deleted`)
        } catch (error) {
          console.error('Failed to save collections:', error)
          // Revert the state change on save failure
          setCollections(collections)
          showError('Save Failed', 'Failed to delete collection. Please try again.')
        }
      } else {
        // In web environment, just show success (collections are stored in memory)
        success('Collection Deleted', `"${collection.name}" has been deleted`)
      }
    } catch (error) {
      console.error('Failed to delete collection:', error)
      showError('Collection Error', 'Failed to delete collection. Please try again.')
    }
  }

  const handleAddImageToCollection = async (collectionId: string, imageId: string) => {
    try {
      const collection = collections.find(c => c.id === collectionId)
      if (!collection) {
        showError('Collection Error', 'Collection not found')
        return
      }

      if (collection.imageIds.includes(imageId)) {
        warning('Already Added', `Image is already in "${collection.name}"`)
        return
      }

      const updatedCollections = collections.map(collection => {
        if (collection.id === collectionId) {
          return {
            ...collection,
            imageIds: [...collection.imageIds, imageId],
            updatedAt: Date.now(),
          }
        }
        return collection
      })
      setCollections(updatedCollections)

      // Show success feedback
      if (!collection.isDefault) {
        success('Added to Collection', `Image added to "${collection.name}"`)
      }

      // Save to file storage
      if (window.electronAPI && typeof window.electronAPI.saveCollections === 'function') {
        try {
          await window.electronAPI.saveCollections(updatedCollections)
        } catch (error) {
          console.error('Failed to save collections:', error)
          // Revert the state change on save failure
          setCollections(collections)
          showError('Save Failed', 'Failed to save collection changes')
        }
      }
    } catch (error) {
      console.error('Failed to add image to collection:', error)
      showError('Collection Error', 'Failed to add image to collection')
    }
  }

  const handleRemoveImageFromCollection = async (collectionId: string, imageId: string) => {
    try {
      const updatedCollections = collections.map(collection => {
        if (collection.id === collectionId) {
          return {
            ...collection,
            imageIds: collection.imageIds.filter(id => id !== imageId),
            updatedAt: Date.now(),
          }
        }
        return collection
      })
      setCollections(updatedCollections)

      // Save to file storage
      if (window.electronAPI && typeof window.electronAPI.saveCollections === 'function') {
        try {
          await window.electronAPI.saveCollections(updatedCollections)
        } catch (error) {
          console.error('Failed to save collections:', error)
          // Revert the state change on save failure
          setCollections(collections)
          showError('Save Failed', 'Failed to save collection changes')
        }
      }
    } catch (error) {
      console.error('Failed to remove image from collection:', error)
      showError('Collection Error', 'Failed to remove image from collection')
    }
  }

  // Filter and sort images based on current filters
  const filteredAndSortedImages = useMemo(() => {
    let filtered = [...images]

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(image =>
        image.prompt.toLowerCase().includes(query) ||
        image.service.toLowerCase().includes(query)
      )
    }

    // Apply service filter
    if (filterService !== 'all') {
      filtered = filtered.filter(image => image.service === filterService)
    }

    // Apply date filter
    if (dateFilter !== 'all') {
      const now = new Date()
      const filterDate = new Date()

      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0)
          break
        case 'week':
          filterDate.setDate(now.getDate() - 7)
          break
        case 'month':
          filterDate.setMonth(now.getMonth() - 1)
          break
      }

      filtered = filtered.filter(image => new Date(image.timestamp) >= filterDate)
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return b.timestamp - a.timestamp
        case 'oldest':
          return a.timestamp - b.timestamp
        case 'prompt':
          return a.prompt.localeCompare(b.prompt)
        default:
          return b.timestamp - a.timestamp
      }
    })

    return filtered
  }, [images, searchQuery, filterService, dateFilter, sortBy])

  const handleRefresh = async () => {
    if (!window.electronAPI) return

    try {
      const result = await window.electronAPI.refreshGeneratedImages()
      if (result.success && result.images) {
        setImages(result.images)
        success('Images Refreshed', 'Image gallery has been updated')
      } else {
        showError('Refresh Failed', result.error || 'Unknown error occurred')
      }
    } catch (error) {
      console.error('Refresh failed:', error)
      showError('Refresh Failed', error instanceof Error ? error.message : 'Unknown error occurred')
    }
  }

  const handleDelete = async (image: GeneratedImage) => {
    if (!image) {
      showError('Delete Failed', 'Invalid image data')
      return
    }

    if (!window.electronAPI) {
      showError('Delete Failed', 'Desktop functionality not available')
      return
    }

    if (!image.filename) {
      // For images without filename, just remove from UI
      setImages(prev => prev.filter(img => img.id !== image.id))

      // Remove from all collections
      const updatedCollections = collections.map(collection => ({
        ...collection,
        imageIds: collection.imageIds.filter(id => id !== image.id),
        updatedAt: Date.now(),
      }))
      setCollections(updatedCollections)

      success('Image Removed', 'Image has been removed from the gallery')
      return
    }

    try {
      const result = await window.electronAPI.deleteGeneratedImage(image.filename)

      if (result.success) {
        // Remove from UI
        setImages(prev => prev.filter(img => img.id !== image.id))

        // Remove from all collections
        const updatedCollections = collections.map(collection => ({
          ...collection,
          imageIds: collection.imageIds.filter(id => id !== image.id),
          updatedAt: Date.now(),
        }))
        setCollections(updatedCollections)

        // Save updated collections
        if (window.electronAPI) {
          try {
            await window.electronAPI.saveCollections(updatedCollections)
          } catch (error) {
            console.error('Failed to update collections after image deletion:', error)
          }
        }

        success('Image Deleted', 'Image has been deleted successfully')
      } else {
        showError('Delete Failed', result.error || 'Unknown error occurred')
      }
    } catch (error) {
      console.error('Delete failed:', error)
      showError('Delete Failed', error instanceof Error ? error.message : 'Unknown error occurred')
    }
  }

  const handleDownload = async (image: GeneratedImage) => {
    if (!window.electronAPI) {
      showError('Download Failed', 'Desktop functionality not available')
      return
    }

    try {
      let base64Data: string

      // Handle different image URL formats
      if (image.url.startsWith('data:image/')) {
        // Already base64 encoded (ImageFX images)
        base64Data = image.url
      } else {
        // Fetch from URL (Pollinations images)
        const response = await fetch(image.url)
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.statusText}`)
        }

        const blob = await response.blob()
        base64Data = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader()
          reader.onload = () => resolve(reader.result as string)
          reader.onerror = () => reject(new Error('Failed to read image data'))
          reader.readAsDataURL(blob)
        })
      }

      // Generate filename with timestamp and service
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)
      const filename = `imagen-${image.service}-${timestamp}.png`

      const result = await window.electronAPI.downloadImage(base64Data, filename)

      if (result.success) {
        success('Image Downloaded', `Saved to: ${result.path}`)
      } else {
        showError('Download Failed', result.error || 'Unknown error occurred')
      }
    } catch (error) {
      console.error('Download failed:', error)
      showError('Download Failed', error instanceof Error ? error.message : 'Unknown error occurred')
    }
  }

  return (
    <ErrorBoundary>
      <div className="flex h-screen bg-app text-white relative">
        {/* Show sidebar only for images tab */}
        {activeTab === 'images' && (
          <Sidebar
            settings={settings}
            onSettingsChange={setSettings}
            onGenerate={handleGenerate}
            isGenerating={isGenerating}
          />
        )}

        <div className={`flex-1 h-full flex flex-col transition-all duration-300 relative ${
          activeTab === 'images' && isRightSidebarOpen ? 'lg:mr-96 mr-0' : 'mr-0'
        }`}>
          {/* Main Tab Navigation */}
          <div className="flex border-b border-matte bg-sidebar">
            <button
              onClick={() => setActiveTab('images')}
              className={`flex items-center gap-2 px-6 py-4 text-sm font-medium transition-colors ${
                activeTab === 'images'
                  ? 'text-accent-primary border-b-2 border-accent-primary bg-accent-primary/5'
                  : 'text-gray-400 hover:text-white hover:bg-matte/50'
              }`}
            >
              <Image className="w-4 h-4" />
              Images
            </button>
            <button
              onClick={() => setActiveTab('chat')}
              className={`flex items-center gap-2 px-6 py-4 text-sm font-medium transition-colors ${
                activeTab === 'chat'
                  ? 'text-accent-primary border-b-2 border-accent-primary bg-accent-primary/5'
                  : 'text-gray-400 hover:text-white hover:bg-matte/50'
              }`}
            >
              <MessageSquare className="w-4 h-4" />
              Chat
            </button>
          </div>

          {/* Content Area */}
          <div className="flex-1 overflow-hidden">
            {activeTab === 'images' ? (
              <ImageGrid
                images={filteredAndSortedImages}
                totalImages={images.length}
                onDownload={handleDownload}
                onDelete={handleDelete}
                onPreview={handlePreview}
                onRefresh={handleRefresh}
                onEdit={handleEdit}
                selectedImages={selectedImages}
                isSelectionMode={isSelectionMode}
                onToggleSelection={handleToggleSelection}
                onToggleSelectionMode={handleToggleSelectionMode}
                onSelectAll={handleSelectAll}
                onDeselectAll={handleDeselectAll}
                onBatchDownload={handleBatchDownload}
                onBatchDelete={handleBatchDelete}
                onBatchExport={handleBatchExport}
                searchQuery={searchQuery}
                onSearchChange={setSearchQuery}
                filterService={filterService}
                onFilterServiceChange={setFilterService}
                sortBy={sortBy}
                onSortChange={setSortBy}
                dateFilter={dateFilter}
                onDateFilterChange={setDateFilter}
                onOpenComparison={handleOpenComparison}
                onOpenCollections={handleOpenCollections}
                collections={collections}
                onAddToCollection={handleAddImageToCollection}
                onCreateCollection={handleCreateCollection}
              />
            ) : (
              <Chat />
            )}
          </div>
        </div>

      {/* Show right sidebar only for images tab */}
      {activeTab === 'images' && (
        <RightSidebar
          isOpen={isRightSidebarOpen}
          onToggle={handleToggleRightSidebar}
          onPromptSelect={handlePromptSelect}
        />
      )}

      <ImagePreview
        image={previewImage}
        isOpen={isPreviewOpen}
        onClose={handleClosePreview}
        onDownload={handleDownload}
        onDelete={handleDelete}
        onEdit={handleEdit}
      />

      <ImageEditor
        image={editingImage}
        isOpen={isEditorOpen}
        onClose={handleCloseEditor}
        onSave={handleSaveEdit}
      />

      <ImageComparison
        images={images}
        isOpen={isComparisonOpen}
        onClose={handleCloseComparison}
        onDownload={handleDownload}
        onDelete={handleDelete}
        onEdit={handleEdit}
      />

      <Collections
        isOpen={isCollectionsOpen}
        onClose={handleCloseCollections}
        images={images}
        collections={collections}
        onCreateCollection={handleCreateCollection}
        onUpdateCollection={handleUpdateCollection}
        onDeleteCollection={handleDeleteCollection}
        onAddImageToCollection={handleAddImageToCollection}
        onRemoveImageFromCollection={handleRemoveImageFromCollection}
        selectedImages={selectedImages}
      />

      <ToastContainer toasts={toasts} onClose={removeToast} />
      </div>
    </ErrorBoundary>
  )
}

export default App
